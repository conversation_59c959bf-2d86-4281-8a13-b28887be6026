import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Palette, MessageSquare, Trash2, Save, Edit3 } from 'lucide-react';

const ContextMenu = ({
  isVisible,
  position,
  onClose,
  targetElement,
  onColorChange,
  onCommentChange,
  onIconChange,
  onPositionChange,
  onDelete,
  onSave
}) => {
  const [selectedColor, setSelectedColor] = useState('#ff0000');
  const [comment, setComment] = useState('');
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showCommentEditor, setShowCommentEditor] = useState(false);
  const [showIconPicker, setShowIconPicker] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState('📍');
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [markerLat, setMarkerLat] = useState('');
  const [markerLng, setMarkerLng] = useState('');
  const [zoomLevel, setZoomLevel] = useState(1);
  const [showPositionEditor, setShowPositionEditor] = useState(false);
  const menuRef = useRef(null);

  const colors = [
    { value: '#ff0000', name: 'Rouge' },
    { value: '#00ff00', name: 'Vert' },
    { value: '#0000ff', name: 'Bleu' }
  ];

  const markerIcons = ['📍', '🎯', '⚠️', '🚩'];

  useEffect(() => {
    if (isVisible && targetElement) {
      // Récupérer les propriétés actuelles de l'élément
      if (targetElement.options) {
        setSelectedColor(targetElement.options.color || '#ff0000');
      }

      // Récupérer le commentaire stocké directement
      const storedComment = targetElement._comment || '';
      setComment(storedComment);

      // Récupérer les coordonnées EXACTES pour les marqueurs
      if (typeof targetElement.getLatLng === 'function') {
        const latlng = targetElement.getLatLng();
        console.log('📍 Coordonnées réelles du marqueur:', latlng.lat, latlng.lng);
        setMarkerLat(latlng.lat.toString());
        setMarkerLng(latlng.lng.toString());
      }

      // Récupérer le niveau de zoom pour adaptation dynamique
      if (window.map && window.map.getZoom) {
        setZoomLevel(window.map.getZoom());
      }
    }
  }, [isVisible, targetElement]);

  const handleColorSelect = (color) => {
    setSelectedColor(color);
    if (onColorChange) {
      onColorChange(color);
    }
    setShowColorPicker(false);
  };

  const handleCommentSave = () => {
    if (onCommentChange) {
      onCommentChange(comment);
    }
    setShowCommentEditor(false);
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
    onClose();
  };

  const handleSave = () => {
    if (onSave) {
      onSave();
    }
    onClose();
  };

  const handleIconSelect = (icon) => {
    setSelectedIcon(icon);
    if (onIconChange) {
      onIconChange(icon);
    }
    setShowIconPicker(false);
  };

  const handlePositionChange = () => {
    if (onPositionChange && markerLat && markerLng) {
      const lat = parseFloat(markerLat);
      const lng = parseFloat(markerLng);

      // Validation des coordonnées
      if (!isNaN(lat) && !isNaN(lng) &&
          lat >= -90 && lat <= 90 &&
          lng >= -180 && lng <= 180) {
        console.log('📍 Nouvelles coordonnées validées:', lat, lng);
        onPositionChange(lat, lng);
      } else {
        alert('Coordonnées invalides!\nLatitude: -90 à +90\nLongitude: -180 à +180');
      }
    }
  };

  // Fonction pour calculer l'aire d'un polygone
  const calculatePolygonArea = (latlngs) => {
    if (!latlngs || latlngs.length < 3) return 0;

    let area = 0;
    const earthRadius = 6371000; // Rayon de la Terre en mètres

    for (let i = 0; i < latlngs.length; i++) {
      const j = (i + 1) % latlngs.length;
      const lat1 = latlngs[i].lat * Math.PI / 180;
      const lat2 = latlngs[j].lat * Math.PI / 180;
      const lng1 = latlngs[i].lng * Math.PI / 180;
      const lng2 = latlngs[j].lng * Math.PI / 180;

      area += (lng2 - lng1) * (2 + Math.sin(lat1) + Math.sin(lat2));
    }

    area = Math.abs(area * earthRadius * earthRadius / 2);
    return area / 1000000; // km²
  };

  // Fonction de sauvegarde (placeholder)
  const saveElement = (element) => {
    if (onSave) {
      onSave();
    }
  };

  // Système de drag proper avec clic maintenu
  const handleMouseDown = (e) => {
    // Vérifier si c'est un clic sur la barre de titre (zone de drag)
    const isDragZone = e.target.closest('.drag-handle') ||
                       (!e.target.closest('input') &&
                        !e.target.closest('button') &&
                        !e.target.closest('textarea'));

    if (isDragZone) {
      e.stopPropagation();
      e.preventDefault();
      setIsDragging(true);

      const rect = menuRef.current.getBoundingClientRect();
      setDragPosition(prev => ({
        ...prev,
        offsetX: e.clientX - rect.left,
        offsetY: e.clientY - rect.top,
        startX: e.clientX,
        startY: e.clientY
      }));
    }
  };

  const handleGlobalMouseMove = (e) => {
    if (isDragging && dragPosition.offsetX !== undefined) {
      e.preventDefault();
      e.stopPropagation();

      const newX = Math.max(0, Math.min(window.innerWidth - 280, e.clientX - dragPosition.offsetX));
      const newY = Math.max(0, Math.min(window.innerHeight - 200, e.clientY - dragPosition.offsetY));

      setDragPosition(prev => ({
        ...prev,
        x: newX,
        y: newY
      }));
    }
  };

  const handleGlobalMouseUp = (e) => {
    if (isDragging) {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);
    }
  };

  useEffect(() => {
    if (isDragging) {
      // Ajouter les événements au document pour capturer même hors fenêtre
      document.addEventListener('mousemove', handleGlobalMouseMove, { capture: true });
      document.addEventListener('mouseup', handleGlobalMouseUp, { capture: true });

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove, { capture: true });
        document.removeEventListener('mouseup', handleGlobalMouseUp, { capture: true });
      };
    }
  }, [isDragging, dragPosition.offsetX]);

  if (!isVisible) return null;

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay invisible pour fermer - capture tous les clics */}
      <div
        className="fixed inset-0 z-[500]"
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          onClose();
        }}
        onMouseDown={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        style={{
          background: 'transparent',
          pointerEvents: 'all'
        }}
      />

      {/* Fenêtre contextuelle complètement isolée */}
      <motion.div
        ref={menuRef}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.2 }}
        className="fixed z-[1000] select-none"
        onMouseDown={handleMouseDown}
        onMouseUp={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onDoubleClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onContextMenu={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onWheel={(e) => {
          e.stopPropagation();
        }}
        style={{
          left: dragPosition.x || Math.min(position.x, window.innerWidth - 280),
          top: dragPosition.y || Math.min(position.y, window.innerHeight - 200),
          width: '260px',
          background: 'rgba(15, 23, 42, 0.98)',
          backdropFilter: 'blur(20px)',
          borderRadius: '16px',
          border: '2px solid rgba(34, 197, 94, 0.4)',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(34, 197, 94, 0.2)',
          cursor: isDragging ? 'grabbing' : (dragPosition.x !== undefined ? 'default' : 'move'),
          pointerEvents: 'all',
          userSelect: 'none'
        }}
      >
        {/* Barre de titre pour drag */}
        <div
          className="drag-handle flex items-center justify-between p-3 border-b border-white/10 cursor-move"
          onMouseDown={(e) => {
            e.stopPropagation();
            handleMouseDown(e);
          }}
        >
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-red-500/60"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500/60"></div>
            <div className="w-3 h-3 rounded-full bg-green-500/60"></div>
          </div>
          <div className="text-xs text-white/60 font-medium flex items-center space-x-2">
            <span>Options Tactiques</span>
            {isDragging && <span className="text-green-400">📱</span>}
          </div>
          <div className="w-4"></div>
        </div>

        {/* Interface verticale moderne */}
        <div className="p-4 space-y-4">

          {/* Interface pour marqueurs - Vertical */}
          {targetElement && (targetElement.options?.icon || targetElement._icon || (typeof targetElement.getLatLng === 'function' && !targetElement.getRadius)) ? (
            <>
              {/* Icônes */}
              <div>
                <div className="text-xs text-white/70 font-medium mb-2">Icône:</div>
                <div className="flex justify-center space-x-2">
                  {markerIcons.map((icon, index) => (
                    <button
                      key={index}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handleIconSelect(icon);
                      }}
                      className="w-10 h-10 hover:bg-white/20 rounded-lg transition-all bg-white/10 hover:scale-110 flex items-center justify-center"
                      title={`Icône ${icon}`}
                    >
                      <span className="text-lg">{icon}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Commentaire */}
              <div>
                <div className="text-xs text-white/70 font-medium mb-2">Commentaire:</div>
                <input
                  type="text"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  onBlur={handleCommentSave}
                  onMouseDown={(e) => e.stopPropagation()}
                  onClick={(e) => e.stopPropagation()}
                  onFocus={(e) => e.stopPropagation()}
                  onKeyDown={(e) => e.stopPropagation()}
                  placeholder="Ajouter un commentaire..."
                  className="w-full px-3 py-2 text-sm bg-white/10 text-white rounded-lg border border-white/20 focus:border-green-400 focus:outline-none placeholder-white/50"
                />
              </div>

              {/* Position */}
              <div>
                <div className="text-xs text-white/70 font-medium mb-2">Position:</div>
                <div className="space-y-2">
                  <input
                    type="number"
                    step="0.000001"
                    value={markerLat}
                    onChange={(e) => setMarkerLat(e.target.value)}
                    onBlur={handlePositionChange}
                    onMouseDown={(e) => e.stopPropagation()}
                    onClick={(e) => e.stopPropagation()}
                    onFocus={(e) => e.stopPropagation()}
                    onKeyDown={(e) => e.stopPropagation()}
                    placeholder="Latitude"
                    className="w-full px-3 py-2 text-sm bg-white/10 text-white rounded-lg border border-white/20 focus:border-green-400 focus:outline-none"
                  />
                  <input
                    type="number"
                    step="0.000001"
                    value={markerLng}
                    onChange={(e) => setMarkerLng(e.target.value)}
                    onBlur={handlePositionChange}
                    onMouseDown={(e) => e.stopPropagation()}
                    onClick={(e) => e.stopPropagation()}
                    onFocus={(e) => e.stopPropagation()}
                    onKeyDown={(e) => e.stopPropagation()}
                    placeholder="Longitude"
                    className="w-full px-3 py-2 text-sm bg-white/10 text-white rounded-lg border border-white/20 focus:border-green-400 focus:outline-none"
                  />
                </div>
              </div>
            </>
          ) : (
            /* Interface pour cercles/polygones */
            <>
              {/* Couleurs */}
              <div>
                <div className="text-xs text-white/70 font-medium mb-2">Couleur:</div>
                <div className="flex justify-center space-x-3">
                  {colors.map((color) => (
                    <button
                      key={color.value}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handleColorSelect(color.value);
                      }}
                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                        selectedColor === color.value
                          ? 'border-white scale-110 shadow-lg'
                          : 'border-gray-400 hover:border-gray-300'
                      }`}
                      style={{ backgroundColor: color.value }}
                      title={color.name}
                    />
                  ))}
                </div>
              </div>

              {/* Commentaire */}
              <div>
                <div className="text-xs text-white/70 font-medium mb-2">Commentaire:</div>
                <input
                  type="text"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  onBlur={handleCommentSave}
                  onMouseDown={(e) => e.stopPropagation()}
                  onClick={(e) => e.stopPropagation()}
                  onFocus={(e) => e.stopPropagation()}
                  onKeyDown={(e) => e.stopPropagation()}
                  placeholder="Commentaire tactique..."
                  className="w-full px-3 py-2 text-sm bg-white/10 text-white rounded-lg border border-white/20 focus:border-green-400 focus:outline-none placeholder-white/50"
                />
              </div>

              {/* Rayon pour cercles */}
              {targetElement && typeof targetElement.getRadius === 'function' && (
                <div>
                  <div className="text-xs text-white/70 font-medium mb-2">Rayon (km):</div>
                  <input
                    type="number"
                    step="0.1"
                    value={(targetElement.getRadius() / 1000).toFixed(2)}
                    onChange={(e) => {
                      const newRadius = parseFloat(e.target.value) * 1000;
                      if (!isNaN(newRadius) && newRadius > 0) {
                        targetElement.setRadius(newRadius);
                        saveElement(targetElement);
                      }
                    }}
                    onMouseDown={(e) => e.stopPropagation()}
                    onClick={(e) => e.stopPropagation()}
                    onFocus={(e) => e.stopPropagation()}
                    onKeyDown={(e) => e.stopPropagation()}
                    placeholder="Rayon en km"
                    className="w-full px-3 py-2 text-sm bg-white/10 text-white rounded-lg border border-white/20 focus:border-green-400 focus:outline-none"
                  />
                </div>
              )}

              {/* Surface pour polygones */}
              {targetElement && typeof targetElement.getLatLngs === 'function' && !targetElement.getRadius && (
                <div>
                  <div className="text-xs text-white/70 font-medium mb-2">Surface (km²):</div>
                  <div className="px-3 py-2 text-sm bg-white/5 text-white/70 rounded-lg border border-white/10">
                    {calculatePolygonArea(targetElement.getLatLngs()[0]).toFixed(2)} km²
                  </div>
                </div>
              )}
            </>
          )}

        </div>

        {/* Bouton supprimer */}
        <div className="border-t border-white/10 p-4">
          <button
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              handleDelete();
            }}
            className="w-full py-3 bg-red-500/20 hover:bg-red-500/40 rounded-lg flex items-center justify-center space-x-2 transition-all text-red-400 hover:text-red-300"
            title="Supprimer définitivement"
          >
            <Trash2 className="w-4 h-4" />
            <span className="text-sm font-medium">Supprimer</span>
          </button>
        </div>
      </motion.div>


    </>
  );
};

export default ContextMenu;
