import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Palette, MessageSquare, Trash2, Save, Edit3 } from 'lucide-react';

const ContextMenu = ({
  isVisible,
  position,
  onClose,
  targetElement,
  onColorChange,
  onCommentChange,
  onIconChange,
  onPositionChange,
  onDelete,
  onSave
}) => {
  const [selectedColor, setSelectedColor] = useState('#ff0000');
  const [comment, setComment] = useState('');
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showCommentEditor, setShowCommentEditor] = useState(false);
  const [showIconPicker, setShowIconPicker] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState('📍');
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [markerLat, setMarkerLat] = useState('');
  const [markerLng, setMarkerLng] = useState('');
  const [zoomLevel, setZoomLevel] = useState(1);
  const [showPositionEditor, setShowPositionEditor] = useState(false);
  const menuRef = useRef(null);

  const colors = [
    { value: '#ff0000', name: 'Rouge' },
    { value: '#00ff00', name: 'Vert' },
    { value: '#0000ff', name: 'Bleu' }
  ];

  const markerIcons = ['📍', '🎯', '⚠️', '🚩'];

  useEffect(() => {
    if (isVisible && targetElement) {
      // Récupérer les propriétés actuelles de l'élément
      if (targetElement.options) {
        setSelectedColor(targetElement.options.color || '#ff0000');
      }

      // Récupérer le commentaire stocké directement
      const storedComment = targetElement._comment || '';
      setComment(storedComment);

      // Récupérer les coordonnées EXACTES pour les marqueurs
      if (typeof targetElement.getLatLng === 'function') {
        const latlng = targetElement.getLatLng();
        console.log('📍 Coordonnées réelles du marqueur:', latlng.lat, latlng.lng);
        setMarkerLat(latlng.lat.toString());
        setMarkerLng(latlng.lng.toString());
      }

      // Récupérer le niveau de zoom pour adaptation dynamique
      if (window.map && window.map.getZoom) {
        setZoomLevel(window.map.getZoom());
      }
    }
  }, [isVisible, targetElement]);

  const handleColorSelect = (color) => {
    setSelectedColor(color);
    if (onColorChange) {
      onColorChange(color);
    }
    setShowColorPicker(false);
  };

  const handleCommentSave = () => {
    if (onCommentChange) {
      onCommentChange(comment);
    }
    setShowCommentEditor(false);
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
    onClose();
  };

  const handleSave = () => {
    if (onSave) {
      onSave();
    }
    onClose();
  };

  const handleIconSelect = (icon) => {
    setSelectedIcon(icon);
    if (onIconChange) {
      onIconChange(icon);
    }
    setShowIconPicker(false);
  };

  const handlePositionChange = () => {
    if (onPositionChange && markerLat && markerLng) {
      const lat = parseFloat(markerLat);
      const lng = parseFloat(markerLng);

      // Validation des coordonnées
      if (!isNaN(lat) && !isNaN(lng) &&
          lat >= -90 && lat <= 90 &&
          lng >= -180 && lng <= 180) {
        console.log('📍 Nouvelles coordonnées validées:', lat, lng);
        onPositionChange(lat, lng);
      } else {
        alert('Coordonnées invalides!\nLatitude: -90 à +90\nLongitude: -180 à +180');
      }
    }
  };

  // Gestionnaires de drag améliorés
  const handleMouseDown = (e) => {
    e.stopPropagation();
    setIsDragging(true);
    const rect = menuRef.current.getBoundingClientRect();
    setDragPosition({
      offsetX: e.clientX - rect.left,
      offsetY: e.clientY - rect.top,
      x: rect.left,
      y: rect.top
    });
  };

  const handleMouseMove = (e) => {
    if (isDragging && dragPosition.offsetX !== undefined) {
      e.preventDefault();
      e.stopPropagation();

      const newX = Math.max(0, Math.min(window.innerWidth - 320, e.clientX - dragPosition.offsetX));
      const newY = Math.max(0, Math.min(window.innerHeight - 120, e.clientY - dragPosition.offsetY));

      setDragPosition(prev => ({
        ...prev,
        x: newX,
        y: newY
      }));
    }
  };

  const handleMouseUp = (e) => {
    e.stopPropagation();
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragPosition.offsetX]);

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay pour fermer le menu - sous la carte */}
      <div
        className="fixed inset-0 z-[999]"
        onClick={onClose}
      />

      {/* Menu contextuel compact et draggable */}
      <motion.div
        ref={menuRef}
        initial={{ opacity: 0, scale: 0.9, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 10 }}
        transition={{ duration: 0.15, ease: "easeOut" }}
        className="fixed z-[1000] overflow-hidden select-none"
        onMouseDown={(e) => {
          e.stopPropagation();
          e.preventDefault();
          handleMouseDown(e);
        }}
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onMouseUp={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onMouseMove={(e) => {
          e.stopPropagation();
        }}
        style={{
          left: dragPosition.x || Math.min(position.x, window.innerWidth - 400),
          top: dragPosition.y || Math.min(position.y, window.innerHeight - 70),
          width: '380px',
          height: '60px',
          transform: `scale(${Math.max(0.9, Math.min(1.1, zoomLevel / 12))})`,
          transformOrigin: 'top left',
          background: 'linear-gradient(90deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%)',
          backdropFilter: 'blur(12px)',
          borderRadius: '30px',
          border: '1px solid rgba(34, 197, 94, 0.3)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
          cursor: isDragging ? 'grabbing' : 'grab'
        }}
      >
        {/* Interface horizontale moderne */}
        <div className="flex items-center h-full px-4 space-x-4">

          {/* Interface pour marqueurs */}
          {targetElement && (targetElement.options?.icon || targetElement._icon || (typeof targetElement.getLatLng === 'function' && !targetElement.getRadius)) ? (
            <>
              {/* Icônes */}
              <div className="flex items-center space-x-2">
                <span className="text-xs text-white/80 font-medium">Icône:</span>
                <div className="flex space-x-1">
                  {markerIcons.map((icon, index) => (
                    <button
                      key={index}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleIconSelect(icon);
                      }}
                      className="w-7 h-7 hover:bg-white/20 rounded-lg transition-all bg-white/10 hover:scale-110 flex items-center justify-center"
                      title={`Icône ${icon}`}
                    >
                      <span className="text-sm">{icon}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Bouton Commentaire */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowCommentEditor(!showCommentEditor);
                }}
                className="flex items-center space-x-1 px-3 py-2 bg-blue-500/20 hover:bg-blue-500/30 rounded-lg transition-all text-blue-300 hover:text-blue-200"
                title="Commentaire"
              >
                <MessageSquare className="w-3 h-3" />
                <span className="text-xs">💬</span>
              </button>

              {/* Bouton Position */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowPositionEditor(!showPositionEditor);
                }}
                className="flex items-center space-x-1 px-3 py-2 bg-green-500/20 hover:bg-green-500/30 rounded-lg transition-all text-green-300 hover:text-green-200"
                title="Position"
              >
                <span className="text-xs">📍</span>
              </button>
            </>
          ) : (
            /* Interface pour cercles/polygones */
            <>
              {/* Couleurs */}
              <div className="flex items-center space-x-2">
                <span className="text-xs text-white/80 font-medium">Couleur:</span>
                <div className="flex space-x-1">
                  {colors.map((color) => (
                    <button
                      key={color.value}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleColorSelect(color.value);
                      }}
                      className={`w-6 h-6 rounded-full border-2 transition-all ${
                        selectedColor === color.value
                          ? 'border-white scale-110 shadow-lg'
                          : 'border-gray-400 hover:border-gray-300'
                      }`}
                      style={{ backgroundColor: color.value }}
                      title={color.name}
                    />
                  ))}
                </div>
              </div>

              {/* Commentaire */}
              <div className="flex items-center space-x-2 flex-1">
                <MessageSquare className="w-4 h-4 text-blue-300" />
                <input
                  type="text"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  onBlur={handleCommentSave}
                  onClick={(e) => e.stopPropagation()}
                  placeholder="Commentaire tactique..."
                  className="flex-1 px-3 py-2 text-xs bg-white/10 text-white rounded-full border border-white/20 focus:border-green-400 focus:outline-none placeholder-white/50 backdrop-blur-sm"
                />
              </div>
            </>
          )}

        </div>

        {/* Bouton supprimer moderne */}
        <div className="pr-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleDelete();
            }}
            className="w-10 h-10 bg-red-500/20 hover:bg-red-500/40 rounded-full flex items-center justify-center transition-all text-red-400 hover:text-red-300 hover:scale-110"
            title="Supprimer"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </motion.div>

      {/* Éditeur de commentaire popup */}
      <AnimatePresence>
        {showCommentEditor && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="fixed z-[1001] bg-slate-800 rounded-lg shadow-xl border border-green-400/30 p-4"
            style={{
              left: (dragPosition.x || position.x) + 50,
              top: (dragPosition.y || position.y) - 80,
              width: '280px'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="text-sm text-white font-medium mb-3">💬 Commentaire Tactique</div>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Saisir commentaire..."
              className="w-full p-3 text-sm bg-white/10 text-white rounded border border-white/20 focus:border-green-400 focus:outline-none resize-none placeholder-white/50"
              rows={3}
              autoFocus
            />
            <div className="flex space-x-2 mt-3">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleCommentSave();
                  setShowCommentEditor(false);
                }}
                className="flex-1 py-2 bg-green-500 hover:bg-green-600 rounded text-white text-xs font-medium transition-all"
              >
                Valider
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowCommentEditor(false);
                }}
                className="flex-1 py-2 bg-gray-500 hover:bg-gray-600 rounded text-white text-xs font-medium transition-all"
              >
                Annuler
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Éditeur de position popup */}
      <AnimatePresence>
        {showPositionEditor && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="fixed z-[1001] bg-slate-800 rounded-lg shadow-xl border border-green-400/30 p-4"
            style={{
              left: (dragPosition.x || position.x) + 50,
              top: (dragPosition.y || position.y) - 120,
              width: '280px'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="text-sm text-white font-medium mb-3">📍 Position Géographique</div>
            <div className="space-y-3">
              <div>
                <label className="text-xs text-white/70 block mb-1">Latitude:</label>
                <input
                  type="number"
                  step="0.000001"
                  value={markerLat}
                  onChange={(e) => setMarkerLat(e.target.value)}
                  className="w-full px-3 py-2 text-sm bg-white/10 text-white rounded border border-white/20 focus:border-green-400 focus:outline-none"
                  placeholder="Ex: 33.589886"
                />
              </div>
              <div>
                <label className="text-xs text-white/70 block mb-1">Longitude:</label>
                <input
                  type="number"
                  step="0.000001"
                  value={markerLng}
                  onChange={(e) => setMarkerLng(e.target.value)}
                  className="w-full px-3 py-2 text-sm bg-white/10 text-white rounded border border-white/20 focus:border-green-400 focus:outline-none"
                  placeholder="Ex: -7.603869"
                />
              </div>
            </div>
            <div className="flex space-x-2 mt-4">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handlePositionChange();
                  setShowPositionEditor(false);
                }}
                className="flex-1 py-2 bg-green-500 hover:bg-green-600 rounded text-white text-xs font-medium transition-all"
              >
                Déplacer
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowPositionEditor(false);
                }}
                className="flex-1 py-2 bg-gray-500 hover:bg-gray-600 rounded text-white text-xs font-medium transition-all"
              >
                Annuler
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ContextMenu;
