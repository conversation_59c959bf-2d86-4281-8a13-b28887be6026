/* Styles pour l'interface tactique C2 moderne */

/* <PERSON><PERSON> de défilement blanche */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: white #2d3748;
}

/* Webkit scrollbar pour Chrome/Safari */
.scrollbar-thin::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background-color: #2d3748;
  border-radius: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: white;
  border-radius: 4px;
  border: 1px solid #2d3748;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #f7fafc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.scrollbar-thin::-webkit-scrollbar-thumb:active {
  background: #e2e8f0;
}

/* Scrollbar pour Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: white #2d3748;
}

/* Popup tactique */
.tactical-popup {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 12px;
  line-height: 1.4;
  color: #2d3748;
  min-width: 150px;
}

.tactical-popup .comment-section {
  background: #e6fffa;
  border-left: 3px solid #4299e1;
  padding: 6px 8px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.tactical-popup .info-section {
  background: #f7fafc;
  padding: 4px 6px;
  border-radius: 4px;
}

.tactical-popup .comment-section strong {
  color: #4299e1;
  font-weight: 600;
}

.tactical-popup .info-section div {
  margin-bottom: 2px;
}

.tactical-popup strong {
  color: #1a202c;
  font-weight: 600;
}

/* Tooltip pour commentaires au survol - Arrière-plan blanc */
.comment-tooltip {
  background: white !important;
  border: 1px solid #2d3748 !important;
  border-radius: 6px !important;
  padding: 6px 10px !important;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  font-size: 11px !important;
  font-weight: 500 !important;
  color: #2d3748 !important;
  text-shadow: none !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  max-width: 200px !important;
}

.comment-tooltip::before {
  border-top-color: white !important;
}

/* Animations pour les outils actifs */
@keyframes pulse-tactical {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse-tactical {
  animation: pulse-tactical 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Effets de survol pour les boutons tactiques */
.tactical-button {
  transition: all 0.2s ease-in-out;
}

.tactical-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.tactical-button:active {
  transform: translateY(0);
}

/* Indicateurs de statut */
.status-indicator {
  position: relative;
  display: inline-block;
}

.status-indicator::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid #fff;
}

.status-online::after {
  background-color: #48bb78;
}

.status-offline::after {
  background-color: #f56565;
}

.status-warning::after {
  background-color: #ed8936;
}

/* Menu contextuel */
.context-menu {
  backdrop-filter: blur(8px);
  background-color: rgba(45, 55, 72, 0.95);
}

.context-menu-item {
  transition: all 0.15s ease-in-out;
}

.context-menu-item:hover {
  background-color: rgba(66, 153, 225, 0.2);
  transform: translateX(2px);
}

/* Styles pour les mesures */
.measurement-display {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  border: 1px solid #718096;
  border-radius: 6px;
  padding: 8px 12px;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  font-weight: 600;
  color: #e2e8f0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.measurement-label {
  color: #90cdf4;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.measurement-value {
  color: #68d391;
  font-size: 12px;
  font-weight: 700;
}

/* Styles pour les zones tactiques */
.tactical-zone {
  stroke-dasharray: none;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.tactical-zone-circle {
  stroke-width: 3;
  fill-opacity: 0.15;
  stroke-opacity: 0.8;
}

.tactical-zone-polygon {
  stroke-width: 2;
  fill-opacity: 0.1;
  stroke-opacity: 0.9;
}

.tactical-zone-line {
  stroke-width: 3;
  stroke-opacity: 0.9;
  fill: none;
}

/* Animations d'entrée */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}

.slide-in-left {
  animation: slideInFromLeft 0.3s ease-out;
}

/* Responsive design */
@media (max-width: 768px) {
  .tactical-popup {
    font-size: 11px;
  }
  
  .measurement-display {
    padding: 6px 8px;
    font-size: 10px;
  }
  
  .context-menu {
    min-width: 180px;
  }
}

/* Tooltips pour mesures instantanées */
.radius-tooltip, .distance-tooltip {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
  border: 1px solid #68d391 !important;
  border-radius: 6px !important;
  padding: 4px 8px !important;
  font-family: 'Courier New', monospace !important;
  font-size: 11px !important;
  font-weight: 600 !important;
  color: #68d391 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.radius-tooltip::before, .distance-tooltip::before {
  border-top-color: #68d391 !important;
}

/* Résultat de mesure */
.measurement-result {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  border: 1px solid #4299e1;
  border-radius: 6px;
  padding: 8px 12px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 12px;
  color: #e2e8f0;
  text-align: center;
  min-width: 120px;
}

.measurement-result strong {
  color: #4299e1;
  display: block;
  margin-bottom: 4px;
}

/* Marqueurs icône seulement */
.icon-only-marker {
  background: transparent !important;
  border: none !important;
}

.icon-only-marker div {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.icon-only-marker:hover div {
  transform: scale(1.2);
  filter: brightness(1.2);
}

/* Trajectoire animée */
.trajectory-line {
  stroke-dasharray: 10, 5;
  animation: dash 2s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -15;
  }
}

/* Thème sombre pour les contrôles */
.dark-theme {
  --bg-primary: #1a202c;
  --bg-secondary: #2d3748;
  --bg-tertiary: #4a5568;
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --text-muted: #a0aec0;
  --border-color: #718096;
  --accent-color: #4299e1;
  --success-color: #48bb78;
  --warning-color: #ed8936;
  --error-color: #f56565;
}
