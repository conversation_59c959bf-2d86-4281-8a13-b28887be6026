import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>hai<PERSON> } from 'lucide-react';

/**
 * Composant d'affichage de la position instantanée (curseur uniquement)
 * Positionné au centre bas de la carte, format horizontal professionnel
 * Se déplace vers le haut quand le composant alerte/log s'affiche
 */
const PositionDisplay = ({ mapInstance, bottomSidebarOpen = false }) => {
  const [mousePosition, setMousePosition] = useState(null);

  // Debug pour surveiller les changements du sidebar
  useEffect(() => {
    console.log('PositionDisplay: bottomSidebarOpen changed to:', bottomSidebarOpen);
  }, [bottomSidebarOpen]);

  // Vérification de sécurité
  if (!mapInstance) {
    return null;
  }

  // Suivi de la position de la souris sur la carte
  useEffect(() => {
    if (!mapInstance || !mapInstance.on) return;

    const handleMouseMove = (e) => {
      // Debug simple pour vérifier les coordonnées
      console.log('🗺️ Position:', e.latlng.lat.toFixed(6), e.latlng.lng.toFixed(6));

      setMousePosition({
        latitude: e.latlng.lat,
        longitude: e.latlng.lng
      });
    };

    const handleMouseOut = () => {
      setMousePosition(null);
    };

    mapInstance.on('mousemove', handleMouseMove);
    mapInstance.on('mouseout', handleMouseOut);

    return () => {
      mapInstance.off('mousemove', handleMouseMove);
      mapInstance.off('mouseout', handleMouseOut);
    };
  }, [mapInstance]);

  // Fonction pour convertir en format géographique DMS
  const formatCoordinateDMS = (value, isLatitude) => {
    if (value === null || value === undefined || isNaN(value)) return '---° --\' --"';

    const absValue = Math.abs(value);
    const degrees = Math.floor(absValue);
    const minutes = Math.floor((absValue - degrees) * 60);
    const seconds = Math.floor(((absValue - degrees) * 60 - minutes) * 60);

    // Déterminer la direction
    let direction;
    if (isLatitude) {
      direction = value >= 0 ? 'N' : 'S';
    } else {
      direction = value >= 0 ? 'E' : 'W';
    }

    return `${degrees.toString().padStart(2, '0')}° ${minutes.toString().padStart(2, '0')}' ${seconds.toString().padStart(2, '0')}" ${direction}`;
  };

  // Position dynamique selon l'état du sidebar bottom
  const getPositionStyle = () => {
    if (bottomSidebarOpen) {
      // Quand le sidebar est ouvert (200px de hauteur), se positionner juste au-dessus
      return {
        bottom: '210px', // 200px (hauteur sidebar) + 10px de marge
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 1001 // Plus haut que le composant alerte/log (z-30)
      };
    } else {
      // Quand le sidebar est fermé, il reste visible avec 40px (200-40=160px caché)
      return {
        bottom: '50px', // 40px (hauteur visible) + 10px de marge
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 1001
      };
    }
  };

  // Animation avec clé pour forcer la re-render
  const positionStyle = getPositionStyle();

  return (
    <motion.div
      key={`position-${bottomSidebarOpen}`} // Force re-render quand sidebar change
      className="fixed z-50 pointer-events-none"
      initial={positionStyle}
      animate={positionStyle}
      transition={{
        duration: 0.4,
        ease: 'easeInOut',
        type: 'spring',
        stiffness: 300,
        damping: 30
      }}
    >
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-c2-gray-400/95 backdrop-blur-sm border-2 border-c2-gray-300 rounded-lg shadow-xl pointer-events-auto"
      >
        {/* Position instantanée - Format horizontal professionnel */}
        <div className="px-4 py-2">
          <div className="flex items-center space-x-4">
            {/* Icône */}
            <div className="flex items-center space-x-2">
              <Crosshair className="w-4 h-4 text-c2-blue" />
              <span className="text-sm font-medium text-c2-white">Position</span>
            </div>

            {/* Coordonnées géographiques */}
            {mousePosition ? (
              <div className="flex items-center space-x-3 text-xs font-mono text-c2-gray-100">
                <span>{formatCoordinateDMS(mousePosition.latitude, true)}</span>
                <span className="text-c2-gray-200">|</span>
                <span>{formatCoordinateDMS(mousePosition.longitude, false)}</span>
              </div>
            ) : (
              <div className="text-sm text-c2-gray-100 italic">
                Survolez la carte pour voir les coordonnées
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default PositionDisplay;
