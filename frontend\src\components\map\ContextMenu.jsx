import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Palette, MessageSquare, Trash2, Save, Edit3 } from 'lucide-react';

const ContextMenu = ({
  isVisible,
  position,
  onClose,
  targetElement,
  onColorChange,
  onCommentChange,
  onIconChange,
  onDelete,
  onSave
}) => {
  const [selectedColor, setSelectedColor] = useState('#ff0000');
  const [comment, setComment] = useState('');
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showCommentEditor, setShowCommentEditor] = useState(false);
  const [showIconPicker, setShowIconPicker] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState('📍');

  const colors = [
    { value: '#ff0000', name: '<PERSON>' },
    { value: '#00ff00', name: 'Vert' },
    { value: '#0000ff', name: 'Bleu' }
  ];

  const markerIcons = ['📍', '🎯', '⚠️', '🚩'];

  useEffect(() => {
    if (isVisible && targetElement) {
      // Récupérer les propriétés actuelles de l'élément
      if (targetElement.options) {
        setSelectedColor(targetElement.options.color || '#ff0000');
      }
      if (targetElement.getPopup && targetElement.getPopup()) {
        setComment(targetElement.getPopup().getContent() || '');
      }
    }
  }, [isVisible, targetElement]);

  const handleColorSelect = (color) => {
    setSelectedColor(color);
    if (onColorChange) {
      onColorChange(color);
    }
    setShowColorPicker(false);
  };

  const handleCommentSave = () => {
    if (onCommentChange) {
      onCommentChange(comment);
    }
    setShowCommentEditor(false);
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
    onClose();
  };

  const handleSave = () => {
    if (onSave) {
      onSave();
    }
    onClose();
  };

  const handleIconSelect = (icon) => {
    setSelectedIcon(icon);
    if (onIconChange) {
      onIconChange(icon);
    }
    setShowIconPicker(false);
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay pour fermer le menu */}
      <div
        className="fixed inset-0 z-[9999]"
        onClick={onClose}
      />

      {/* Menu contextuel moderne */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 10 }}
        transition={{ duration: 0.2, ease: "easeOut" }}
        className="fixed z-[10000] overflow-hidden"
        style={{
          left: Math.min(position.x, window.innerWidth - 280),
          top: Math.min(position.y, window.innerHeight - 350),
          minWidth: '260px',
          maxHeight: '320px',
          background: 'linear-gradient(135deg, rgba(45, 55, 72, 0.95) 0%, rgba(74, 85, 104, 0.95) 100%)',
          backdropFilter: 'blur(20px)',
          borderRadius: '16px',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05)'
        }}
      >
        {/* Interface moderne */}
        <div className="p-4">

          {/* Couleur - seulement pour cercles et polygones */}
          {targetElement && !targetElement.options?.icon && (
            <div className="mb-4">
              <div className="text-sm font-medium text-white mb-3 flex items-center">
                <Palette className="w-4 h-4 mr-2 text-blue-400" />
                Couleur
              </div>
              <div className="flex space-x-3">
                {colors.map((color) => (
                  <button
                    key={color.value}
                    onClick={() => handleColorSelect(color.value)}
                    className={`w-10 h-10 rounded-full border-3 transition-all duration-200 ${
                      selectedColor === color.value
                        ? 'border-white scale-110 shadow-lg'
                        : 'border-gray-400 hover:border-gray-300 hover:scale-105'
                    }`}
                    style={{
                      backgroundColor: color.value,
                      boxShadow: selectedColor === color.value ? `0 0 20px ${color.value}40` : 'none'
                    }}
                    title={color.name}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Commentaire */}
          <div className="mb-4">
            <div className="text-sm font-medium text-white mb-3 flex items-center">
              <MessageSquare className="w-4 h-4 mr-2 text-green-400" />
              Commentaire
            </div>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              onBlur={handleCommentSave}
              placeholder="Ajouter un commentaire tactique..."
              className="w-full p-3 text-sm bg-white/10 text-white rounded-lg resize-none border border-white/20 focus:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-400/30 placeholder-gray-400 backdrop-blur-sm"
              rows={2}
            />
          </div>

          {/* Icônes pour marqueurs seulement */}
          {targetElement && (targetElement.options?.icon || targetElement._icon || (typeof targetElement.getLatLng === 'function' && !targetElement.getRadius)) && (
            <div className="mb-4">
              <div className="text-sm font-medium text-white mb-3 flex items-center">
                <span className="text-yellow-400 mr-2">🎯</span>
                Icône
              </div>
              <div className="grid grid-cols-4 gap-3">
                {markerIcons.map((icon, index) => (
                  <button
                    key={index}
                    onClick={() => handleIconSelect(icon)}
                    className="p-3 text-2xl hover:bg-white/20 rounded-xl transition-all duration-200 bg-white/10 hover:scale-110 border border-white/10 hover:border-white/30"
                    title={`Icône ${icon}`}
                  >
                    {icon}
                  </button>
                ))}
              </div>
            </div>
          )}

        </div>

        {/* Action de suppression moderne */}
        <div className="border-t border-white/10 p-4">
          <button
            onClick={handleDelete}
            className="w-full p-3 flex items-center justify-center space-x-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 rounded-xl transition-all duration-200 text-white font-medium shadow-lg hover:shadow-xl hover:scale-105"
          >
            <Trash2 className="w-4 h-4" />
            <span>Supprimer</span>
          </button>
        </div>
      </motion.div>
    </>
  );
};

export default ContextMenu;
