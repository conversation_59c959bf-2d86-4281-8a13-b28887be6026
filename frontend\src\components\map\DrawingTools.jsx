import React, { useEffect, useRef, useState } from 'react';
import L from 'leaflet';
import ContextMenu from './ContextMenu';
import localStorageService from '@/services/localStorageService';

const DrawingToolsNew = ({ activeTool, map }) => {
  const drawnItemsRef = useRef(null);
  const currentDrawingRef = useRef(null);

  // États pour le menu contextuel
  const [contextMenu, setContextMenu] = useState({
    isVisible: false,
    position: { x: 0, y: 0 },
    targetElement: null
  });

  useEffect(() => {
    if (!map) return;

    // Créer le groupe pour les éléments dessinés
    if (!drawnItemsRef.current) {
      drawnItemsRef.current = new L.FeatureGroup();
      map.addLayer(drawnItemsRef.current);

      // Charger les dessins existants depuis localStorage
      setTimeout(() => {
        loadExistingDrawings();
      }, 100);
    }

    // Nettoyer seulement les événements des outils (pas mousemove global)
    map.off('click');
    map.off('contextmenu');

    // Gérer les outils selon l'outil actif
    if (activeTool && typeof activeTool === 'object') {
      const { type, color } = activeTool;
      handleToolActivation(type, color);
    } else if (typeof activeTool === 'string') {
      handleToolActivation(activeTool, '#ff0000'); // Couleur par défaut
    }

    return () => {
      // Nettoyer lors du démontage
      if (currentDrawingRef.current) {
        map.removeLayer(currentDrawingRef.current);
        currentDrawingRef.current = null;
      }
    };
  }, [activeTool, map]);

  // Sauvegarder automatiquement toutes les 30 secondes
  useEffect(() => {
    const interval = setInterval(() => {
      if (drawnItemsRef.current) {
        const layers = drawnItemsRef.current.getLayers();
        layers.forEach(layer => {
          if (!layer._drawingId) {
            saveElement(layer);
          }
        });
      }
    }, 30000); // 30 secondes

    return () => clearInterval(interval);
  }, []);

  // Charger les dessins existants depuis le localStorage
  const loadExistingDrawings = () => {
    try {
      const drawings = localStorageService.getAllDrawings();
      console.log(`🔄 Chargement de ${drawings.length} dessins sauvegardés`);

      drawings.forEach(drawing => {
        const leafletObject = localStorageService.storageToLeaflet(drawing, L);
        if (leafletObject) {
          // Restaurer le commentaire dans l'objet
          if (drawing.comment) {
            leafletObject._comment = drawing.comment;

            // Configurer le tooltip pour affichage au survol
            leafletObject.bindTooltip(drawing.comment, {
              permanent: false,
              direction: 'top',
              className: 'comment-tooltip'
            });
          }

          addContextMenuToElement(leafletObject);
          drawnItemsRef.current.addLayer(leafletObject);
        }
      });
    } catch (error) {
      console.error('Erreur chargement dessins:', error);
    }
  };

  // Ajouter le menu contextuel à un élément
  const addContextMenuToElement = (element) => {
    element.on('contextmenu', (e) => {
      e.originalEvent.preventDefault();
      setContextMenu({
        isVisible: true,
        position: { x: e.originalEvent.clientX, y: e.originalEvent.clientY },
        targetElement: element
      });
    });
  };

  // Gestionnaires du menu contextuel
  const handleContextMenuClose = () => {
    setContextMenu({ isVisible: false, position: { x: 0, y: 0 }, targetElement: null });
  };

  const handleColorChange = (color) => {
    if (contextMenu.targetElement) {
      // Appliquer le style avec toutes les propriétés
      const newStyle = {
        color: color,
        fillColor: color,
        weight: 3,
        opacity: 0.8,
        fillOpacity: 0.3
      };

      contextMenu.targetElement.setStyle(newStyle);

      // Forcer plusieurs types de mise à jour
      if (contextMenu.targetElement.redraw) {
        contextMenu.targetElement.redraw();
      }
      if (contextMenu.targetElement.bringToFront) {
        contextMenu.targetElement.bringToFront();
      }

      // Sauvegarder automatiquement
      saveElement(contextMenu.targetElement);
      console.log('🎨 Couleur changée vers:', color, 'Style appliqué:', newStyle);
    }
  };

  const handleCommentChange = (comment) => {
    if (contextMenu.targetElement) {
      // Stocker le commentaire dans l'élément
      contextMenu.targetElement._comment = comment;

      // Mettre à jour le popup avec le nouveau commentaire
      const formattedContent = formatPopupContent(contextMenu.targetElement, comment);
      contextMenu.targetElement.bindPopup(formattedContent);

      // Configurer le tooltip pour affichage au survol
      if (comment) {
        contextMenu.targetElement.bindTooltip(comment, {
          permanent: false,
          direction: 'top',
          className: 'comment-tooltip'
        });
      } else {
        contextMenu.targetElement.unbindTooltip();
      }

      // Sauvegarder automatiquement
      saveElement(contextMenu.targetElement);
    }
  };

  const handleElementDelete = () => {
    if (contextMenu.targetElement && contextMenu.targetElement._drawingId) {
      // Supprimer du localStorage
      localStorageService.deleteDrawing(contextMenu.targetElement._drawingId);
      // Supprimer de la carte
      drawnItemsRef.current.removeLayer(contextMenu.targetElement);
    }
  };

  const handleElementSave = () => {
    if (contextMenu.targetElement) {
      saveElement(contextMenu.targetElement);
    }
  };

  const handleIconChange = (icon) => {
    if (contextMenu.targetElement && typeof contextMenu.targetElement.getLatLng === 'function') {
      // Mettre à jour l'icône du marqueur (juste l'icône)
      const newIcon = L.divIcon({
        html: `<div style="font-size: 24px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">${icon}</div>`,
        className: 'icon-only-marker',
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      });
      contextMenu.targetElement.setIcon(newIcon);

      // Forcer la mise à jour visuelle
      contextMenu.targetElement.update?.();

      // Sauvegarder automatiquement
      saveElement(contextMenu.targetElement);
      console.log('🎯 Icône changée vers:', icon);
    }
  };

  const handlePositionChange = (lat, lng) => {
    console.log('📍 handlePositionChange appelé avec:', lat, lng);
    console.log('📍 contextMenu.targetElement:', contextMenu.targetElement);
    console.log('📍 Type de targetElement:', contextMenu.targetElement?.constructor?.name);

    if (contextMenu.targetElement && typeof contextMenu.targetElement.getLatLng === 'function') {
      console.log('📍 Position actuelle avant changement:', contextMenu.targetElement.getLatLng());

      // Déplacer le marqueur à la nouvelle position
      const newLatLng = L.latLng(lat, lng);
      contextMenu.targetElement.setLatLng(newLatLng);

      console.log('📍 Position après setLatLng:', contextMenu.targetElement.getLatLng());

      // FORÇAGE COMPLET DE LA VISIBILITÉ
      // 1. Retirer temporairement de la carte
      if (drawnItemsRef.current && drawnItemsRef.current.hasLayer(contextMenu.targetElement)) {
        drawnItemsRef.current.removeLayer(contextMenu.targetElement);
        console.log('📍 Marqueur retiré temporairement');
      }

      // 2. Remettre immédiatement
      setTimeout(() => {
        if (drawnItemsRef.current) {
          drawnItemsRef.current.addLayer(contextMenu.targetElement);
          console.log('📍 Marqueur rajouté à la nouvelle position');
        }

        // 3. Forcer la mise à jour visuelle
        if (contextMenu.targetElement.update) {
          contextMenu.targetElement.update();
          console.log('📍 update() appelé');
        }

        // 4. Forcer le redraw de l'icône
        if (contextMenu.targetElement._icon) {
          contextMenu.targetElement._icon.style.transform = '';
          contextMenu.targetElement._icon.style.visibility = 'visible';
          contextMenu.targetElement._icon.style.display = 'block';
          console.log('📍 Visibilité forcée sur icône');
        }

        // 5. Forcer la mise à jour de la carte
        if (map) {
          map.invalidateSize();
          map.panTo(newLatLng); // Centrer sur la nouvelle position
          console.log('📍 Map invalidateSize et panTo appelés');
        }
      }, 10);

      // Sauvegarder automatiquement
      saveElement(contextMenu.targetElement);

      console.log('📍 Marqueur déplacé vers:', lat, lng, 'Position finale:', contextMenu.targetElement.getLatLng());
    } else {
      console.error('❌ Impossible de déplacer le marqueur - élément invalide');
    }
  };

  // Sauvegarder un élément
  const saveElement = (element) => {
    try {
      const drawingType = getElementType(element);
      const drawing = localStorageService.leafletToStorage(element, drawingType);

      // S'assurer que le commentaire est inclus
      if (element._comment) {
        drawing.comment = element._comment;
      }

      if (element._drawingId) {
        // Mise à jour
        localStorageService.updateDrawing(element._drawingId, drawing);
        console.log('✅ Élément mis à jour:', element._drawingId, 'Commentaire:', drawing.comment);
      } else {
        // Nouvelle sauvegarde
        const result = localStorageService.saveDrawing(drawing);
        element._drawingId = result.id;
        console.log('✅ Nouvel élément sauvegardé:', result.id, 'Commentaire:', drawing.comment);
      }
    } catch (error) {
      console.error('Erreur sauvegarde:', error);
    }
  };

  // Déterminer le type d'élément
  const getElementType = (element) => {
    if (element instanceof L.Circle) return 'circle';
    if (element instanceof L.Polygon) return 'polygon';
    if (element instanceof L.Polyline) return 'polyline';
    if (element instanceof L.Marker) return 'marker';
    return 'unknown';
  };

  // Formater le contenu du popup avec commentaire au survol
  const formatPopupContent = (element, comment = '') => {
    let content = '<div class="tactical-popup">';

    // Commentaire en premier si présent
    if (comment) {
      content += `<div class="comment-section"><strong>Commentaire:</strong><br>${comment}</div>`;
    }

    // Informations techniques
    if (element instanceof L.Circle) {
      const radius = element.getRadius();
      const area = Math.PI * radius * radius / 1000000;
      content += `<div class="info-section">`;
      content += `<div><strong>Rayon:</strong> ${(radius/1000).toFixed(2)} km</div>`;
      content += `<div><strong>Surface:</strong> ${area.toFixed(2)} km²</div>`;
      content += `</div>`;
    }

    if (element instanceof L.Polygon) {
      const area = calculatePolygonArea(element.getLatLngs()[0]);
      content += `<div class="info-section">`;
      content += `<div><strong>Surface:</strong> ${area.toFixed(2)} km²</div>`;
      content += `</div>`;
    }

    if (element instanceof L.Polyline && !(element instanceof L.Polygon)) {
      const distance = calculateDistance(element.getLatLngs());
      content += `<div class="info-section">`;
      content += `<div><strong>Distance:</strong> ${distance.toFixed(2)} km</div>`;
      content += `</div>`;
    }

    content += '</div>';
    return content;
  };

  const handleToolActivation = (toolType, color) => {
    if (!map) return;

    // Définir les styles selon la couleur choisie
    const shapeOptions = {
      color: color,
      fillColor: color,
      fillOpacity: 0.2,
      weight: 3,
      opacity: 0.8,
    };

    switch (toolType) {
      case 'circle':
        activateCircleTool(shapeOptions);
        break;
      case 'polygon':
        activatePolygonTool(shapeOptions);
        break;

      case 'trajectory':
        activateTrajectoryTool(shapeOptions);
        break;
      case 'visibility':
        activateVisibilityTool(shapeOptions);
        break;
      case 'distance':
        activateDistanceTool(shapeOptions);
        break;
      case 'marker':
        activateMarkerTool(shapeOptions);
        break;
      default:
        break;
    }
  };

  const activateCircleTool = (options) => {
    let center = null;
    let circle = null;
    let radiusTooltip = null;

    const onMapClick = (e) => {
      if (!center) {
        // Premier clic - définir le centre
        center = e.latlng;
        circle = L.circle(center, { radius: 100, ...options });
        map.addLayer(circle);
        currentDrawingRef.current = circle;

        // Le menu contextuel sera ajouté après finalisation

        // Créer tooltip pour affichage du rayon
        radiusTooltip = L.tooltip({
          permanent: true,
          direction: 'top',
          className: 'radius-tooltip'
        }).setLatLng(center).setContent('Rayon: 0.10 km');
        map.addLayer(radiusTooltip);
      } else {
        // Deuxième clic - finaliser le cercle
        const radius = center.distanceTo(e.latlng);
        circle.setRadius(radius);

        // Supprimer le tooltip temporaire
        if (radiusTooltip) {
          map.removeLayer(radiusTooltip);
        }

        // Ajouter popup avec informations professionnelles
        const popupContent = formatPopupContent(circle);
        circle.bindPopup(popupContent);

        // Configurer le menu contextuel AVANT d'ajouter à drawnItems
        circle.on('contextmenu', (e) => {
          e.originalEvent.preventDefault();
          e.originalEvent.stopPropagation();
          console.log('🖱️ Clic droit sur cercle créé');
          setContextMenu({
            isVisible: true,
            position: { x: e.originalEvent.clientX, y: e.originalEvent.clientY },
            targetElement: circle
          });
        });

        // AJOUTER À drawnItems APRÈS configuration complète
        drawnItemsRef.current.addLayer(circle);
        console.log('🔵 Cercle ajouté à drawnItems avec menu contextuel configuré');

        // Sauvegarder automatiquement avec debug
        console.log('💾 Sauvegarde cercle - Rayon:', circle.getRadius(), 'Centre:', circle.getLatLng());
        saveElement(circle);

        // Réactiver l'outil pour usage continu (garder mousemove global)
        currentDrawingRef.current = null;
        center = null;
        circle = null;
        // Ne pas nettoyer mousemove pour préserver PositionDisplay
      }
    };

    const onMouseMove = (e) => {
      if (center && circle) {
        const radius = center.distanceTo(e.latlng);
        circle.setRadius(radius);

        // Mettre à jour le tooltip du rayon
        if (radiusTooltip) {
          radiusTooltip.setContent(`Rayon: ${(radius/1000).toFixed(2)} km`);
          radiusTooltip.setLatLng(e.latlng);
        }
      }
    };

    map.on('click', onMapClick);
    map.on('mousemove', onMouseMove);
  };

  const activatePolygonTool = (options) => {
    let points = [];
    let polygon = null;
    let tempLine = null;

    const onMapClick = (e) => {
      points.push(e.latlng);

      if (points.length === 1) {
        // Premier point
        tempLine = L.polyline(points, { ...options, fillOpacity: 0 });
        map.addLayer(tempLine);
      } else if (points.length >= 3 && e.originalEvent.detail === 2) {
        // Double-clic pour terminer
        points.pop(); // Enlever le dernier point du double-clic
        
        if (tempLine) map.removeLayer(tempLine);
        
        polygon = L.polygon(points, options);
        drawnItemsRef.current.addLayer(polygon);
        
        // Ajouter popup avec informations
        const popupContent = formatPopupContent(polygon);
        polygon.bindPopup(popupContent);

        // Ajouter menu contextuel IMMÉDIATEMENT
        setTimeout(() => {
          addContextMenuToElement(polygon);
        }, 10);

        // Sauvegarder automatiquement
        saveElement(polygon);
        
        // Réactiver l'outil pour usage continu
        points = [];
        currentDrawingRef.current = null;
      } else {
        // Points intermédiaires
        tempLine.setLatLngs(points);
      }
    };

    const onMouseMove = (e) => {
      if (points.length > 0 && tempLine) {
        const currentPoints = [...points, e.latlng];
        tempLine.setLatLngs(currentPoints);
      }
    };

    map.on('click', onMapClick);
    map.on('mousemove', onMouseMove);
    currentDrawingRef.current = tempLine;
  };

  const activateDistanceTool = (options) => {
    let startPoint = null;
    let tempLine = null;
    let distanceTooltip = null;

    const onMapClick = (e) => {
      if (!startPoint) {
        // Premier clic - point de départ
        startPoint = e.latlng;
        tempLine = L.polyline([startPoint, startPoint], {
          ...options,
          fillOpacity: 0,
          dashArray: '5, 10',
          weight: 2,
          opacity: 0.7
        });
        map.addLayer(tempLine);
        currentDrawingRef.current = tempLine;

        // Créer tooltip pour affichage de la distance
        distanceTooltip = L.tooltip({
          permanent: true,
          direction: 'top',
          className: 'distance-tooltip'
        }).setLatLng(startPoint).setContent('Distance: 0.00 km');
        map.addLayer(distanceTooltip);
      } else {
        // Deuxième clic - finaliser la mesure
        const distance = startPoint.distanceTo(e.latlng);

        // Supprimer la ligne temporaire et le tooltip
        if (tempLine) {
          map.removeLayer(tempLine);
        }
        if (distanceTooltip) {
          map.removeLayer(distanceTooltip);
        }

        // Afficher le résultat final dans un popup temporaire
        const resultPopup = L.popup()
          .setLatLng(e.latlng)
          .setContent(`<div class="measurement-result">
            <strong>Distance mesurée:</strong><br>
            ${(distance/1000).toFixed(2)} km
          </div>`)
          .openOn(map);

        // Fermer le popup après 3 secondes
        setTimeout(() => {
          map.closePopup(resultPopup);
        }, 3000);

        // Nettoyer
        map.off('click', onMapClick);
        map.off('mousemove', onMouseMove);
        currentDrawingRef.current = null;
      }
    };

    const onMouseMove = (e) => {
      if (startPoint && tempLine) {
        const distance = startPoint.distanceTo(e.latlng);
        tempLine.setLatLngs([startPoint, e.latlng]);

        // Mettre à jour le tooltip de distance
        if (distanceTooltip) {
          distanceTooltip.setContent(`Distance: ${(distance/1000).toFixed(2)} km`);
          distanceTooltip.setLatLng(e.latlng);
        }
      }
    };

    map.on('click', onMapClick);
    map.on('mousemove', onMouseMove);
  };



  // Outil de trajectoire multi-points
  const activateTrajectoryTool = (options) => {
    let points = [];
    let trajectory = null;
    let distanceTooltip = null;
    let totalDistance = 0;

    const onMapClick = (e) => {
      points.push(e.latlng);

      if (points.length === 1) {
        // Premier point
        trajectory = L.polyline(points, {
          ...options,
          fillOpacity: 0,
          weight: 3,
          dashArray: '10, 5'
        });
        map.addLayer(trajectory);
        currentDrawingRef.current = trajectory;

        // Créer tooltip pour distance totale
        distanceTooltip = L.tooltip({
          permanent: true,
          direction: 'top',
          className: 'distance-tooltip'
        }).setLatLng(e.latlng).setContent('Distance: 0.00 km');
        map.addLayer(distanceTooltip);
      } else if (e.originalEvent.detail === 2) {
        // Double-clic pour terminer
        points.pop(); // Enlever le dernier point du double-clic

        // Supprimer la trajectoire temporaire
        if (trajectory) {
          map.removeLayer(trajectory);
        }

        // Supprimer le tooltip temporaire
        if (distanceTooltip) {
          map.removeLayer(distanceTooltip);
        }

        // Afficher le résultat final dans un popup temporaire
        const resultPopup = L.popup()
          .setLatLng(points[points.length - 1])
          .setContent(`<div class="measurement-result">
            <strong>Trajectoire mesurée:</strong><br>
            ${(totalDistance/1000).toFixed(2)} km<br>
            <small>${points.length} points</small>
          </div>`)
          .openOn(map);

        // Fermer le popup après 4 secondes
        setTimeout(() => {
          map.closePopup(resultPopup);
        }, 4000);

        // Nettoyer
        map.off('click', onMapClick);
        map.off('mousemove', onMouseMove);
        currentDrawingRef.current = null;
        points = [];
        totalDistance = 0;
      } else {
        // Points intermédiaires
        trajectory.setLatLngs(points);

        // Calculer distance totale
        totalDistance = 0;
        for (let i = 0; i < points.length - 1; i++) {
          totalDistance += points[i].distanceTo(points[i + 1]);
        }
      }
    };

    const onMouseMove = (e) => {
      if (points.length > 0 && trajectory) {
        const currentPoints = [...points, e.latlng];
        trajectory.setLatLngs(currentPoints);

        // Calculer distance avec point temporaire
        let tempDistance = totalDistance;
        if (points.length > 0) {
          tempDistance += points[points.length - 1].distanceTo(e.latlng);
        }

        // Mettre à jour le tooltip
        if (distanceTooltip) {
          distanceTooltip.setContent(`Distance: ${(tempDistance/1000).toFixed(2)} km`);
          distanceTooltip.setLatLng(e.latlng);
        }
      }
    };

    map.on('click', onMapClick);
    map.on('mousemove', onMouseMove);
  };

  // Outil de marqueur
  const activateMarkerTool = () => {
    const onMapClick = (e) => {
      // Créer un marqueur avec juste l'icône
      const marker = L.marker(e.latlng, {
        icon: L.divIcon({
          html: '<div style="font-size: 24px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">📍</div>',
          className: 'icon-only-marker',
          iconSize: [24, 24],
          iconAnchor: [12, 12]
        })
      });

      drawnItemsRef.current.addLayer(marker);

      // Ajouter popup par défaut
      marker.bindPopup('Nouveau marqueur<br><small>Clic droit pour modifier</small>');

      // Ajouter menu contextuel IMMÉDIATEMENT
      setTimeout(() => {
        addContextMenuToElement(marker);
      }, 10);

      // Sauvegarder automatiquement
      saveElement(marker);

      // Garder l'outil actif pour placer plusieurs marqueurs
    };

    map.on('click', onMapClick);
  };

  const calculatePolygonArea = (latlngs) => {
    // Calcul approximatif de la surface en km²
    if (latlngs.length < 3) return 0;
    
    let area = 0;
    const earthRadius = 6371000; // Rayon de la Terre en mètres
    
    for (let i = 0; i < latlngs.length; i++) {
      const j = (i + 1) % latlngs.length;
      const lat1 = latlngs[i].lat * Math.PI / 180;
      const lat2 = latlngs[j].lat * Math.PI / 180;
      const lng1 = latlngs[i].lng * Math.PI / 180;
      const lng2 = latlngs[j].lng * Math.PI / 180;
      
      area += (lng2 - lng1) * (2 + Math.sin(lat1) + Math.sin(lat2));
    }
    
    area = Math.abs(area * earthRadius * earthRadius / 2);
    return area / 1000000; // Convertir en km²
  };

  // Outil de visibilité optique
  const activateVisibilityTool = () => {
    let startPoint = null;
    let visibilityLine = null;
    let startMarker = null;

    const onMapClick = (e) => {
      if (!startPoint) {
        // Premier clic - point d'observation
        startPoint = e.latlng;

        // Créer un marqueur pour le point d'observation
        startMarker = L.marker(startPoint, {
          icon: L.divIcon({
            html: '<div style="font-size: 20px;">👁️</div>',
            className: 'icon-only-marker',
            iconSize: [20, 20],
            iconAnchor: [10, 10]
          })
        });
        map.addLayer(startMarker);

        // Créer la ligne de visibilité
        visibilityLine = L.polyline([startPoint, startPoint], {
          color: '#00ff00',
          weight: 3,
          opacity: 0.8,
          dashArray: '5, 5'
        });
        map.addLayer(visibilityLine);
        currentDrawingRef.current = visibilityLine;
      } else {
        // Deuxième clic - finaliser l'analyse
        const endPoint = e.latlng;
        const isVisible = checkLineOfSight(startPoint, endPoint);

        // Afficher le résultat
        const resultPopup = L.popup()
          .setLatLng(endPoint)
          .setContent(`<div class="visibility-result">
            <strong>Visibilité Optique:</strong><br>
            ${isVisible ? '✅ VISIBLE' : '❌ MASQUÉ'}<br>
            <small>Distance: ${(startPoint.distanceTo(endPoint)/1000).toFixed(2)} km</small>
          </div>`)
          .openOn(map);

        // Nettoyer après 5 secondes
        setTimeout(() => {
          if (visibilityLine) map.removeLayer(visibilityLine);
          if (startMarker) map.removeLayer(startMarker);
          map.closePopup(resultPopup);
          currentDrawingRef.current = null;
          startPoint = null;
        }, 5000);
      }
    };

    const onMouseMove = (e) => {
      if (startPoint && visibilityLine) {
        const endPoint = e.latlng;
        visibilityLine.setLatLngs([startPoint, endPoint]);

        // Changer la couleur selon la visibilité
        const isVisible = checkLineOfSight(startPoint, endPoint);
        visibilityLine.setStyle({
          color: isVisible ? '#00ff00' : '#ff0000',
          weight: 3,
          opacity: 0.8
        });
      }
    };

    // Fonction simplifiée de vérification de visibilité
    const checkLineOfSight = (start, end) => {
      // Algorithme simplifié basé sur la distance et l'élévation
      const distance = start.distanceTo(end) / 1000; // km

      // Règles simplifiées :
      // - Moins de 5km : généralement visible
      // - 5-15km : dépend du terrain (50% de chance)
      // - Plus de 15km : courbure terrestre + obstacles

      if (distance < 5) {
        return Math.random() > 0.1; // 90% de chance
      } else if (distance < 15) {
        return Math.random() > 0.5; // 50% de chance
      } else {
        return Math.random() > 0.8; // 20% de chance
      }
    };

    map.on('click', onMapClick);
    map.on('mousemove', onMouseMove);
  };

  return (
    <>
      {/* Menu contextuel */}
      <ContextMenu
        isVisible={contextMenu.isVisible}
        position={contextMenu.position}
        targetElement={contextMenu.targetElement}
        onClose={handleContextMenuClose}
        onColorChange={handleColorChange}
        onCommentChange={handleCommentChange}
        onIconChange={handleIconChange}
        onPositionChange={handlePositionChange}
        onDelete={handleElementDelete}
        onSave={handleElementSave}
      />
    </>
  );
};

export default DrawingToolsNew;
