import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Palette, MessageSquare, Trash2, Save, Edit3 } from 'lucide-react';

const ContextMenu = ({
  isVisible,
  position,
  onClose,
  targetElement,
  onColorChange,
  onCommentChange,
  onIconChange,
  onPositionChange,
  onDelete,
  onSave
}) => {
  const [selectedColor, setSelectedColor] = useState('#ff0000');
  const [comment, setComment] = useState('');
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showCommentEditor, setShowCommentEditor] = useState(false);
  const [showIconPicker, setShowIconPicker] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState('📍');
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [markerLat, setMarkerLat] = useState('');
  const [markerLng, setMarkerLng] = useState('');
  const menuRef = useRef(null);

  const colors = [
    { value: '#ff0000', name: 'Rouge' },
    { value: '#00ff00', name: 'Vert' },
    { value: '#0000ff', name: 'Bleu' }
  ];

  const markerIcons = ['📍', '🎯', '⚠️', '🚩'];

  useEffect(() => {
    if (isVisible && targetElement) {
      // Récupérer les propriétés actuelles de l'élément
      if (targetElement.options) {
        setSelectedColor(targetElement.options.color || '#ff0000');
      }

      // Récupérer le commentaire stocké directement
      const storedComment = targetElement._comment || '';
      setComment(storedComment);

      // Récupérer les coordonnées pour les marqueurs
      if (typeof targetElement.getLatLng === 'function') {
        const latlng = targetElement.getLatLng();
        setMarkerLat(latlng.lat.toFixed(6));
        setMarkerLng(latlng.lng.toFixed(6));
      }
    }
  }, [isVisible, targetElement]);

  const handleColorSelect = (color) => {
    setSelectedColor(color);
    if (onColorChange) {
      onColorChange(color);
    }
    setShowColorPicker(false);
  };

  const handleCommentSave = () => {
    if (onCommentChange) {
      onCommentChange(comment);
    }
    setShowCommentEditor(false);
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
    onClose();
  };

  const handleSave = () => {
    if (onSave) {
      onSave();
    }
    onClose();
  };

  const handleIconSelect = (icon) => {
    setSelectedIcon(icon);
    if (onIconChange) {
      onIconChange(icon);
    }
    setShowIconPicker(false);
  };

  const handlePositionChange = () => {
    if (onPositionChange && markerLat && markerLng) {
      const lat = parseFloat(markerLat);
      const lng = parseFloat(markerLng);
      if (!isNaN(lat) && !isNaN(lng)) {
        onPositionChange(lat, lng);
      }
    }
  };

  // Gestionnaires de drag
  const handleMouseDown = (e) => {
    setIsDragging(true);
    const rect = menuRef.current.getBoundingClientRect();
    setDragPosition({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging]);

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay pour fermer le menu */}
      <div
        className="fixed inset-0 z-[9999]"
        onClick={onClose}
      />

      {/* Menu contextuel horizontal et fluide */}
      <motion.div
        ref={menuRef}
        initial={{ opacity: 0, scale: 0.9, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 10 }}
        transition={{ duration: 0.15, ease: "easeOut" }}
        className="fixed z-[10000] overflow-hidden select-none flex items-center"
        onMouseDown={handleMouseDown}
        style={{
          left: Math.min(position.x, window.innerWidth - 400),
          top: Math.min(position.y, window.innerHeight - 80),
          minWidth: '380px',
          height: '60px',
          background: 'linear-gradient(90deg, #1a202c 0%, #2d3748 100%)',
          borderRadius: '30px',
          border: '1px solid #68d391',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(104, 211, 145, 0.2)',
          cursor: isDragging ? 'grabbing' : 'grab'
        }}
      >
        {/* Interface horizontale */}
        <div className="flex items-center space-x-4 px-6 py-2 flex-1">

          {/* Couleur - seulement pour cercles et polygones */}
          {targetElement && !targetElement.options?.icon && (
            <div className="flex items-center space-x-2">
              <span className="text-xs text-white font-medium">Couleur:</span>
              <div className="flex space-x-1">
                {colors.map((color) => (
                  <button
                    key={color.value}
                    onClick={() => handleColorSelect(color.value)}
                    className={`w-5 h-5 rounded border-2 transition-all ${
                      selectedColor === color.value
                        ? 'border-white scale-110'
                        : 'border-gray-400 hover:border-gray-300'
                    }`}
                    style={{ backgroundColor: color.value }}
                    title={color.name}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Commentaire */}
          <div className="flex items-center space-x-2 flex-1">
            <span className="text-xs text-white font-medium">Commentaire:</span>
            <input
              type="text"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              onBlur={handleCommentSave}
              placeholder="Ajouter un commentaire..."
              className="flex-1 px-3 py-1 text-xs bg-white/90 text-black rounded-full border border-white/30 focus:border-blue-400 focus:outline-none"
            />
          </div>

          {/* Icônes et coordonnées pour marqueurs seulement */}
          {targetElement && (targetElement.options?.icon || targetElement._icon || (typeof targetElement.getLatLng === 'function' && !targetElement.getRadius)) && (
            <>
              <div className="flex items-center space-x-2">
                <span className="text-xs text-white font-medium">Icône:</span>
                <div className="flex space-x-1">
                  {markerIcons.map((icon, index) => (
                    <button
                      key={index}
                      onClick={() => handleIconSelect(icon)}
                      className="w-6 h-6 hover:bg-white/20 rounded-full transition-all bg-white/10 hover:scale-110 flex items-center justify-center"
                      title={`Icône ${icon}`}
                    >
                      <span className="text-xs">{icon}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <span className="text-xs text-white font-medium">Position:</span>
                <input
                  type="number"
                  step="0.000001"
                  value={markerLat}
                  onChange={(e) => setMarkerLat(e.target.value)}
                  onBlur={handlePositionChange}
                  className="w-20 px-2 py-1 text-xs bg-white/90 text-black rounded border focus:border-blue-400 focus:outline-none"
                  placeholder="Lat"
                />
                <input
                  type="number"
                  step="0.000001"
                  value={markerLng}
                  onChange={(e) => setMarkerLng(e.target.value)}
                  onBlur={handlePositionChange}
                  className="w-20 px-2 py-1 text-xs bg-white/90 text-black rounded border focus:border-blue-400 focus:outline-none"
                  placeholder="Lng"
                />
              </div>
            </>
          )}

        </div>

        {/* Bouton supprimer circulaire à droite */}
        <div className="pr-2">
          <button
            onClick={handleDelete}
            className="w-10 h-10 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-all hover:scale-110 shadow-lg"
            title="Supprimer"
          >
            <Trash2 className="w-4 h-4 text-white" />
          </button>
        </div>
      </motion.div>
    </>
  );
};

export default ContextMenu;
