import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Palette, MessageSquare, Trash2, Save, Edit3 } from 'lucide-react';

const ContextMenu = ({
  isVisible,
  position,
  onClose,
  targetElement,
  onColorChange,
  onCommentChange,
  onIconChange,
  onPositionChange,
  onDelete,
  onSave
}) => {
  const [selectedColor, setSelectedColor] = useState('#ff0000');
  const [comment, setComment] = useState('');
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showCommentEditor, setShowCommentEditor] = useState(false);
  const [showIconPicker, setShowIconPicker] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState('📍');
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [markerLat, setMarkerLat] = useState('');
  const [markerLng, setMarkerLng] = useState('');
  const [zoomLevel, setZoomLevel] = useState(1);
  const [showPositionEditor, setShowPositionEditor] = useState(false);
  const menuRef = useRef(null);

  const colors = [
    { value: '#ff0000', name: 'Rouge' },
    { value: '#00ff00', name: 'Vert' },
    { value: '#0000ff', name: 'Bleu' }
  ];

  const markerIcons = ['📍', '🎯', '⚠️', '🚩'];

  useEffect(() => {
    if (isVisible && targetElement) {
      // Récupérer les propriétés actuelles de l'élément
      if (targetElement.options) {
        setSelectedColor(targetElement.options.color || '#ff0000');
      }

      // Récupérer le commentaire stocké directement
      const storedComment = targetElement._comment || '';
      setComment(storedComment);

      // Récupérer les coordonnées EXACTES pour les marqueurs
      if (typeof targetElement.getLatLng === 'function') {
        const latlng = targetElement.getLatLng();
        console.log('📍 Coordonnées réelles du marqueur:', latlng.lat, latlng.lng);
        setMarkerLat(latlng.lat.toString());
        setMarkerLng(latlng.lng.toString());
      }

      // Récupérer le rayon pour les cercles
      if (typeof targetElement.getRadius === 'function') {
        const radius = targetElement.getRadius();
        setRadiusValue((radius / 1000).toFixed(2));
        console.log('📏 Rayon récupéré:', (radius / 1000).toFixed(2), 'km');
      }

      // Récupérer le niveau de zoom pour adaptation dynamique
      if (window.map && window.map.getZoom) {
        setZoomLevel(window.map.getZoom());
      }
    }
  }, [isVisible, targetElement]);

  const handleColorSelect = (color) => {
    setSelectedColor(color);
    if (onColorChange) {
      onColorChange(color);
    }
    setShowColorPicker(false);
  };

  const handleCommentSave = () => {
    if (onCommentChange) {
      onCommentChange(comment);
    }
    setShowCommentEditor(false);
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
    onClose();
  };

  const handleSave = () => {
    if (onSave) {
      onSave();
    }
    onClose();
  };

  const handleIconSelect = (icon) => {
    setSelectedIcon(icon);
    if (onIconChange) {
      onIconChange(icon);
    }
    setShowIconPicker(false);
  };

  const handlePositionChange = () => {
    if (onPositionChange && markerLat && markerLng) {
      const lat = parseFloat(markerLat);
      const lng = parseFloat(markerLng);

      // Validation des coordonnées
      if (!isNaN(lat) && !isNaN(lng) &&
          lat >= -90 && lat <= 90 &&
          lng >= -180 && lng <= 180) {
        console.log('📍 ContextMenu: Nouvelles coordonnées validées:', lat, lng);
        console.log('📍 ContextMenu: Appel onPositionChange avec:', lat, lng);
        onPositionChange(lat, lng);
      } else {
        console.error('❌ Coordonnées invalides:', lat, lng);
        alert('Coordonnées invalides!\nLatitude: -90 à +90\nLongitude: -180 à +180');
      }
    }
  };

  // Fonction pour calculer l'aire d'un polygone
  const calculatePolygonArea = (latlngs) => {
    if (!latlngs || latlngs.length < 3) return 0;

    let area = 0;
    const earthRadius = 6371000; // Rayon de la Terre en mètres

    for (let i = 0; i < latlngs.length; i++) {
      const j = (i + 1) % latlngs.length;
      const lat1 = latlngs[i].lat * Math.PI / 180;
      const lat2 = latlngs[j].lat * Math.PI / 180;
      const lng1 = latlngs[i].lng * Math.PI / 180;
      const lng2 = latlngs[j].lng * Math.PI / 180;

      area += (lng2 - lng1) * (2 + Math.sin(lat1) + Math.sin(lat2));
    }

    area = Math.abs(area * earthRadius * earthRadius / 2);
    return area / 1000000; // km²
  };

  // Fonction de sauvegarde (placeholder)
  const saveElement = (element) => {
    if (onSave) {
      onSave();
    }
  };

  // Conversion coordonnées décimales vers DMS
  const decimalToDMS = (decimal) => {
    const abs = Math.abs(decimal);
    const degrees = Math.floor(abs);
    const minutes = Math.floor((abs - degrees) * 60);
    const seconds = Math.round(((abs - degrees) * 60 - minutes) * 60);
    return { degrees, minutes, seconds };
  };

  // Conversion DMS vers coordonnées décimales
  const dmsToDecimal = (degrees, minutes, seconds, isNegative = false) => {
    const decimal = degrees + minutes / 60 + seconds / 3600;
    return isNegative ? -decimal : decimal;
  };

  // États pour les coordonnées DMS
  const [latDMS, setLatDMS] = useState({ degrees: 0, minutes: 0, seconds: 0, isNegative: false });
  const [lngDMS, setLngDMS] = useState({ degrees: 0, minutes: 0, seconds: 0, isNegative: false });

  // État local pour le rayon
  const [radiusValue, setRadiusValue] = useState('');

  // Mise à jour des DMS quand les coordonnées changent
  useEffect(() => {
    if (markerLat && markerLng) {
      const lat = parseFloat(markerLat);
      const lng = parseFloat(markerLng);

      if (!isNaN(lat)) {
        const latDMSData = decimalToDMS(lat);
        setLatDMS({ ...latDMSData, isNegative: lat < 0 });
      }

      if (!isNaN(lng)) {
        const lngDMSData = decimalToDMS(lng);
        setLngDMS({ ...lngDMSData, isNegative: lng < 0 });
      }
    }
  }, [markerLat, markerLng]);

  // Mise à jour des coordonnées décimales depuis DMS
  const updateCoordinatesFromDMS = () => {
    const newLat = dmsToDecimal(latDMS.degrees, latDMS.minutes, latDMS.seconds, latDMS.isNegative);
    const newLng = dmsToDecimal(lngDMS.degrees, lngDMS.minutes, lngDMS.seconds, lngDMS.isNegative);

    setMarkerLat(newLat.toFixed(6));
    setMarkerLng(newLng.toFixed(6));

    if (onPositionChange) {
      onPositionChange(newLat, newLng);
    }
  };

  // Système de drag proper avec clic maintenu
  const handleMouseDown = (e) => {
    // Vérifier si c'est un clic sur la barre de titre (zone de drag)
    const isDragZone = e.target.closest('.drag-handle') ||
                       (!e.target.closest('input') &&
                        !e.target.closest('button') &&
                        !e.target.closest('textarea'));

    if (isDragZone) {
      e.stopPropagation();
      e.preventDefault();
      setIsDragging(true);

      const rect = menuRef.current.getBoundingClientRect();
      setDragPosition(prev => ({
        ...prev,
        offsetX: e.clientX - rect.left,
        offsetY: e.clientY - rect.top,
        startX: e.clientX,
        startY: e.clientY
      }));
    }
  };

  const handleGlobalMouseMove = (e) => {
    if (isDragging && dragPosition.offsetX !== undefined) {
      e.preventDefault();
      e.stopPropagation();

      const newX = Math.max(0, Math.min(window.innerWidth - 280, e.clientX - dragPosition.offsetX));
      const newY = Math.max(0, Math.min(window.innerHeight - 200, e.clientY - dragPosition.offsetY));

      setDragPosition(prev => ({
        ...prev,
        x: newX,
        y: newY
      }));
    }
  };

  const handleGlobalMouseUp = (e) => {
    if (isDragging) {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);
    }
  };

  useEffect(() => {
    if (isDragging) {
      // Ajouter les événements au document pour capturer même hors fenêtre
      document.addEventListener('mousemove', handleGlobalMouseMove, { capture: true });
      document.addEventListener('mouseup', handleGlobalMouseUp, { capture: true });

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove, { capture: true });
        document.removeEventListener('mouseup', handleGlobalMouseUp, { capture: true });
      };
    }
  }, [isDragging, dragPosition.offsetX]);

  if (!isVisible) return null;

  if (!isVisible) return null;

  // Créer un portal pour isolation totale
  const portalContent = (
    <div
      className="context-menu-portal"
      onMouseDown={(e) => e.stopPropagation()}
      onMouseUp={(e) => e.stopPropagation()}
      onMouseMove={(e) => e.stopPropagation()}
      onClick={(e) => e.stopPropagation()}
      onDoubleClick={(e) => e.stopPropagation()}
      onContextMenu={(e) => e.stopPropagation()}
      onWheel={(e) => e.stopPropagation()}
      onKeyDown={(e) => e.stopPropagation()}
      onKeyUp={(e) => e.stopPropagation()}
      style={{
        position: 'fixed',
        inset: 0,
        pointerEvents: 'none',
        zIndex: 10000
      }}
    >
      {/* Overlay invisible pour fermer */}
      <div
        className="fixed inset-0"
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          onClose();
        }}
        style={{
          background: 'transparent',
          pointerEvents: 'all'
        }}
      />

      {/* Fenêtre contextuelle avec dimensions fixes */}
      <motion.div
        ref={menuRef}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.2 }}
        className="fixed select-none overflow-hidden"
        onMouseDown={(e) => {
          e.stopPropagation();
          e.preventDefault();
          handleMouseDown(e);
        }}
        onMouseUp={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onMouseMove={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onDoubleClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onContextMenu={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onWheel={(e) => {
          e.stopPropagation();
        }}
        style={{
          left: Math.max(10, Math.min(dragPosition.x || position.x, window.innerWidth - 420)),
          top: Math.max(10, Math.min(dragPosition.y || position.y, window.innerHeight - 140)),
          width: targetElement && typeof targetElement.getLatLng === 'function' && !targetElement.getRadius ? '400px' : '330px',
          height: targetElement && typeof targetElement.getLatLng === 'function' && !targetElement.getRadius ? '140px' : '120px',
          background: 'linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(15, 23, 42, 0.95) 100%)',
          backdropFilter: 'blur(25px)',
          borderRadius: '20px',
          border: '1px solid rgba(34, 197, 94, 0.3)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
          cursor: isDragging ? 'grabbing' : 'default',
          pointerEvents: 'all',
          userSelect: 'none',
          zIndex: 1,
          overflow: 'hidden'
        }}
      >
        {/* Interface verticale moderne avec bouton supprimer à droite */}
        <div className="flex h-full w-full"
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
            handleMouseDown(e);
          }}
        >
          {/* Contenu principal */}
          <div className="flex-1 p-3 space-y-2 overflow-hidden">

            {/* Interface conditionnelle */}
            {targetElement && (
              <>
                {/* MARQUEURS */}
                {typeof targetElement.getLatLng === 'function' && !targetElement.getRadius && (
                  <>
                    {/* Ligne 1: Icônes + Commentaire */}
                    <div className="flex items-center space-x-2">
                      {/* Icônes */}
                      <div className="flex items-center space-x-1 bg-white/5 rounded-lg px-2 py-1">
                        <span className="text-xs text-white/80">🎯</span>
                        {markerIcons.map((icon, index) => (
                          <button
                            key={index}
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              console.log('🎯 Clic icône:', icon);
                              if (onIconChange) {
                                onIconChange(icon);
                                console.log('🎯 onIconChange appelé avec:', icon);
                              }
                            }}
                            className="w-7 h-7 hover:bg-white/20 rounded-lg transition-all bg-white/10 hover:scale-110 flex items-center justify-center"
                            title={`Icône ${icon}`}
                          >
                            <span className="text-sm">{icon}</span>
                          </button>
                        ))}
                      </div>

                      {/* Commentaire */}
                      <div className="flex items-center space-x-1 bg-white/5 rounded-lg px-2 py-1 flex-1">
                        <span className="text-xs text-white/80">💬</span>
                        <input
                          type="text"
                          value={comment}
                          onChange={(e) => setComment(e.target.value)}
                          onBlur={() => {
                            console.log('💬 Sauvegarde commentaire:', comment);
                            if (onCommentChange) {
                              onCommentChange(comment);
                            }
                          }}
                          onKeyDown={(e) => {
                            e.stopPropagation();
                            if (e.key === 'Enter') {
                              if (onCommentChange) {
                                onCommentChange(comment);
                              }
                              e.target.blur();
                            }
                          }}
                          onMouseDown={(e) => e.stopPropagation()}
                          onClick={(e) => e.stopPropagation()}
                          onFocus={(e) => e.stopPropagation()}
                          placeholder="Commentaire..."
                          className="flex-1 px-2 py-1 text-xs bg-transparent text-white border-none focus:outline-none placeholder-white/50"
                          maxLength={25}
                        />
                      </div>
                    </div>

                    {/* Ligne 2: Position DMS */}
                    <div className="space-y-1">
                      {/* Latitude DMS */}
                      <div className="flex items-center space-x-1">
                        <span className="text-xs text-white/80 w-6">📍</span>
                        <span className="text-xs text-white/60 w-8">Lat:</span>
                        <div className="flex items-center space-x-1 bg-white/5 rounded-lg px-2 py-1 flex-1">
                          <input
                            type="number"
                            min="0"
                            max="90"
                            value={latDMS.degrees}
                            onChange={(e) => {
                              e.stopPropagation();
                              setLatDMS(prev => ({ ...prev, degrees: parseInt(e.target.value) || 0 }));
                            }}
                            onBlur={updateCoordinatesFromDMS}
                            onKeyDown={(e) => {
                              e.stopPropagation();
                              if (e.key === 'Enter') {
                                updateCoordinatesFromDMS();
                                e.target.blur();
                              }
                            }}
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                            placeholder="33"
                            className="w-8 px-1 py-1 text-xs bg-white/10 text-white rounded border border-white/20 focus:border-green-400 focus:outline-none text-center"
                          />
                          <span className="text-xs text-white/60">°</span>
                          <input
                            type="number"
                            min="0"
                            max="59"
                            value={latDMS.minutes}
                            onChange={(e) => {
                              e.stopPropagation();
                              setLatDMS(prev => ({ ...prev, minutes: parseInt(e.target.value) || 0 }));
                            }}
                            onBlur={updateCoordinatesFromDMS}
                            onKeyDown={(e) => {
                              e.stopPropagation();
                              if (e.key === 'Enter') {
                                updateCoordinatesFromDMS();
                                e.target.blur();
                              }
                            }}
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                            placeholder="35"
                            className="w-8 px-1 py-1 text-xs bg-white/10 text-white rounded border border-white/20 focus:border-green-400 focus:outline-none text-center"
                          />
                          <span className="text-xs text-white/60">'</span>
                          <input
                            type="number"
                            min="0"
                            max="59"
                            value={latDMS.seconds}
                            onChange={(e) => {
                              e.stopPropagation();
                              setLatDMS(prev => ({ ...prev, seconds: parseInt(e.target.value) || 0 }));
                            }}
                            onBlur={updateCoordinatesFromDMS}
                            onKeyDown={(e) => {
                              e.stopPropagation();
                              if (e.key === 'Enter') {
                                updateCoordinatesFromDMS();
                                e.target.blur();
                              }
                            }}
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                            placeholder="23"
                            className="w-8 px-1 py-1 text-xs bg-white/10 text-white rounded border border-white/20 focus:border-green-400 focus:outline-none text-center"
                          />
                          <span className="text-xs text-white/60">"</span>
                          <select
                            value={latDMS.isNegative ? 'S' : 'N'}
                            onChange={(e) => {
                              e.stopPropagation();
                              setLatDMS(prev => ({ ...prev, isNegative: e.target.value === 'S' }));
                              setTimeout(updateCoordinatesFromDMS, 10);
                            }}
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                            className="w-8 px-1 py-1 text-xs bg-white/10 text-white rounded border border-white/20 focus:border-green-400 focus:outline-none text-center"
                          >
                            <option value="N">N</option>
                            <option value="S">S</option>
                          </select>
                        </div>
                      </div>

                      {/* Longitude DMS */}
                      <div className="flex items-center space-x-1">
                        <span className="text-xs text-white/80 w-6"></span>
                        <span className="text-xs text-white/60 w-8">Lng:</span>
                        <div className="flex items-center space-x-1 bg-white/5 rounded-lg px-2 py-1 flex-1">
                          <input
                            type="number"
                            min="0"
                            max="180"
                            value={lngDMS.degrees}
                            onChange={(e) => {
                              e.stopPropagation();
                              setLngDMS(prev => ({ ...prev, degrees: parseInt(e.target.value) || 0 }));
                            }}
                            onBlur={updateCoordinatesFromDMS}
                            onKeyDown={(e) => {
                              e.stopPropagation();
                              if (e.key === 'Enter') {
                                updateCoordinatesFromDMS();
                                e.target.blur();
                              }
                            }}
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                            placeholder="07"
                            className="w-8 px-1 py-1 text-xs bg-white/10 text-white rounded border border-white/20 focus:border-green-400 focus:outline-none text-center"
                          />
                          <span className="text-xs text-white/60">°</span>
                          <input
                            type="number"
                            min="0"
                            max="59"
                            value={lngDMS.minutes}
                            onChange={(e) => {
                              e.stopPropagation();
                              setLngDMS(prev => ({ ...prev, minutes: parseInt(e.target.value) || 0 }));
                            }}
                            onBlur={updateCoordinatesFromDMS}
                            onKeyDown={(e) => {
                              e.stopPropagation();
                              if (e.key === 'Enter') {
                                updateCoordinatesFromDMS();
                                e.target.blur();
                              }
                            }}
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                            placeholder="36"
                            className="w-8 px-1 py-1 text-xs bg-white/10 text-white rounded border border-white/20 focus:border-green-400 focus:outline-none text-center"
                          />
                          <span className="text-xs text-white/60">'</span>
                          <input
                            type="number"
                            min="0"
                            max="59"
                            value={lngDMS.seconds}
                            onChange={(e) => {
                              e.stopPropagation();
                              setLngDMS(prev => ({ ...prev, seconds: parseInt(e.target.value) || 0 }));
                            }}
                            onBlur={updateCoordinatesFromDMS}
                            onKeyDown={(e) => {
                              e.stopPropagation();
                              if (e.key === 'Enter') {
                                updateCoordinatesFromDMS();
                                e.target.blur();
                              }
                            }}
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                            placeholder="14"
                            className="w-8 px-1 py-1 text-xs bg-white/10 text-white rounded border border-white/20 focus:border-green-400 focus:outline-none text-center"
                          />
                          <span className="text-xs text-white/60">"</span>
                          <select
                            value={lngDMS.isNegative ? 'W' : 'E'}
                            onChange={(e) => {
                              e.stopPropagation();
                              setLngDMS(prev => ({ ...prev, isNegative: e.target.value === 'W' }));
                              setTimeout(updateCoordinatesFromDMS, 10);
                            }}
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                            className="w-8 px-1 py-1 text-xs bg-white/10 text-white rounded border border-white/20 focus:border-green-400 focus:outline-none text-center"
                          >
                            <option value="E">E</option>
                            <option value="W">W</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {/* CERCLES ET POLYGONES */}
                {(typeof targetElement.getRadius === 'function' || typeof targetElement.getLatLngs === 'function') && (
                  <>
                    {/* Ligne 1: Couleurs + Commentaire */}
                    <div className="flex items-center space-x-2">
                      {/* Couleurs */}
                      <div className="flex items-center space-x-1 bg-white/5 rounded-lg px-2 py-1">
                        <span className="text-xs text-white/80">🎨</span>
                        {colors.map((color) => (
                          <button
                            key={color.value}
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              console.log('🎨 Clic couleur:', color.value);
                              if (onColorChange) {
                                onColorChange(color.value);
                                console.log('🎨 onColorChange appelé avec:', color.value);
                              }
                            }}
                            className={`w-6 h-6 rounded-full border-2 transition-all ${
                              selectedColor === color.value
                                ? 'border-white scale-110 shadow-lg'
                                : 'border-gray-400 hover:border-gray-300'
                            }`}
                            style={{ backgroundColor: color.value }}
                            title={color.name}
                          />
                        ))}
                      </div>

                      {/* Commentaire */}
                      <div className="flex items-center space-x-1 bg-white/5 rounded-lg px-2 py-1 flex-1">
                        <span className="text-xs text-white/80">💬</span>
                        <input
                          type="text"
                          value={comment}
                          onChange={(e) => setComment(e.target.value)}
                          onBlur={() => {
                            console.log('💬 Sauvegarde commentaire:', comment);
                            if (onCommentChange) {
                              onCommentChange(comment);
                            }
                          }}
                          onKeyDown={(e) => {
                            e.stopPropagation();
                            if (e.key === 'Enter') {
                              if (onCommentChange) {
                                onCommentChange(comment);
                              }
                              e.target.blur();
                            }
                          }}
                          onMouseDown={(e) => e.stopPropagation()}
                          onClick={(e) => e.stopPropagation()}
                          onFocus={(e) => e.stopPropagation()}
                          placeholder="Commentaire..."
                          className="flex-1 px-2 py-1 text-xs bg-transparent text-white border-none focus:outline-none placeholder-white/50"
                          maxLength={20}
                        />
                      </div>
                    </div>

                    {/* Ligne 2: Rayon ou Surface */}
                    {typeof targetElement.getRadius === 'function' && (
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-white/80 w-6">📏</span>
                        <div className="flex items-center space-x-1 bg-white/5 rounded-lg px-2 py-1 flex-1">
                          <span className="text-xs text-white/60 w-12">Rayon:</span>
                          <input
                            type="text"
                            value={radiusValue}
                            onChange={(e) => {
                              e.stopPropagation();
                              setRadiusValue(e.target.value);
                              // Mise à jour temps réel
                              const newRadius = parseFloat(e.target.value) * 1000;
                              if (!isNaN(newRadius) && newRadius > 0) {
                                targetElement.setRadius(newRadius);
                              }
                            }}
                            onBlur={(e) => {
                              // Application finale au blur
                              const newRadius = parseFloat(radiusValue) * 1000;
                              if (!isNaN(newRadius) && newRadius > 0) {
                                targetElement.setRadius(newRadius);
                                if (onSave) onSave();
                                console.log('📏 Rayon modifié:', (newRadius/1000).toFixed(2), 'km');
                              } else {
                                // Restaurer la valeur précédente si invalide
                                setRadiusValue((targetElement.getRadius() / 1000).toFixed(2));
                              }
                            }}
                            onKeyDown={(e) => {
                              e.stopPropagation();
                              if (e.key === 'Enter') {
                                const newRadius = parseFloat(radiusValue) * 1000;
                                if (!isNaN(newRadius) && newRadius > 0) {
                                  targetElement.setRadius(newRadius);
                                  if (onSave) onSave();
                                  console.log('📏 Rayon appliqué:', (newRadius/1000).toFixed(2), 'km');
                                }
                                e.target.blur();
                              }
                            }}
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                            placeholder="1.50"
                            className="w-16 px-1 py-1 text-xs bg-white/10 text-white rounded border border-white/20 focus:border-green-400 focus:outline-none"
                          />
                          <span className="text-xs text-white/60">km</span>
                        </div>
                      </div>
                    )}

                    {/* Surface pour polygones */}
                    {typeof targetElement.getLatLngs === 'function' && !targetElement.getRadius && (
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-white/80 w-6">📐</span>
                        <div className="flex items-center space-x-1 bg-white/5 rounded-lg px-2 py-1 flex-1">
                          <span className="text-xs text-white/60 w-12">Surface:</span>
                          <span className="text-xs text-white/90 font-mono bg-white/10 px-2 py-1 rounded">
                            {calculatePolygonArea(targetElement.getLatLngs()[0]).toFixed(2)} km²
                          </span>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>

          {/* Bouton supprimer circulaire à droite */}
          <div className="flex items-center justify-center w-14 h-full">
            <button
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                console.log('🗑️ Clic supprimer');
                if (onDelete) {
                  onDelete();
                  console.log('🗑️ onDelete appelé');
                }
                // Fermer la fenêtre après suppression
                onClose();
              }}
              className="w-10 h-10 bg-red-500/20 hover:bg-red-500/40 rounded-full flex items-center justify-center transition-all text-red-400 hover:text-red-300 hover:scale-110 border-2 border-red-500/40 hover:border-red-500/60"
              title="Supprimer"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>

        </div>
      </motion.div>
    </div>
  );

  // Utiliser un portal pour isolation totale
  return createPortal(portalContent, document.body);
};

export default ContextMenu;
