import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Palette, MessageSquare, Trash2, Save, Edit3 } from 'lucide-react';

const ContextMenu = ({
  isVisible,
  position,
  onClose,
  targetElement,
  onColorChange,
  onCommentChange,
  onIconChange,
  onPositionChange,
  onDelete,
  onSave
}) => {
  const [selectedColor, setSelectedColor] = useState('#ff0000');
  const [comment, setComment] = useState('');
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showCommentEditor, setShowCommentEditor] = useState(false);
  const [showIconPicker, setShowIconPicker] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState('📍');
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [markerLat, setMarkerLat] = useState('');
  const [markerLng, setMarkerLng] = useState('');
  const [zoomLevel, setZoomLevel] = useState(1);
  const menuRef = useRef(null);

  const colors = [
    { value: '#ff0000', name: 'Rouge' },
    { value: '#00ff00', name: 'Vert' },
    { value: '#0000ff', name: 'Bleu' }
  ];

  const markerIcons = ['📍', '🎯', '⚠️', '🚩'];

  useEffect(() => {
    if (isVisible && targetElement) {
      // Récupérer les propriétés actuelles de l'élément
      if (targetElement.options) {
        setSelectedColor(targetElement.options.color || '#ff0000');
      }

      // Récupérer le commentaire stocké directement
      const storedComment = targetElement._comment || '';
      setComment(storedComment);

      // Récupérer les coordonnées pour les marqueurs
      if (typeof targetElement.getLatLng === 'function') {
        const latlng = targetElement.getLatLng();
        setMarkerLat(latlng.lat.toFixed(6));
        setMarkerLng(latlng.lng.toFixed(6));
      }

      // Récupérer le niveau de zoom pour adaptation dynamique
      if (window.map && window.map.getZoom) {
        setZoomLevel(window.map.getZoom());
      }
    }
  }, [isVisible, targetElement]);

  const handleColorSelect = (color) => {
    setSelectedColor(color);
    if (onColorChange) {
      onColorChange(color);
    }
    setShowColorPicker(false);
  };

  const handleCommentSave = () => {
    if (onCommentChange) {
      onCommentChange(comment);
    }
    setShowCommentEditor(false);
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
    onClose();
  };

  const handleSave = () => {
    if (onSave) {
      onSave();
    }
    onClose();
  };

  const handleIconSelect = (icon) => {
    setSelectedIcon(icon);
    if (onIconChange) {
      onIconChange(icon);
    }
    setShowIconPicker(false);
  };

  const handlePositionChange = () => {
    if (onPositionChange && markerLat && markerLng) {
      const lat = parseFloat(markerLat);
      const lng = parseFloat(markerLng);
      if (!isNaN(lat) && !isNaN(lng)) {
        onPositionChange(lat, lng);
      }
    }
  };

  // Gestionnaires de drag améliorés
  const handleMouseDown = (e) => {
    e.stopPropagation();
    setIsDragging(true);
    const rect = menuRef.current.getBoundingClientRect();
    setDragPosition({
      offsetX: e.clientX - rect.left,
      offsetY: e.clientY - rect.top,
      x: rect.left,
      y: rect.top
    });
  };

  const handleMouseMove = (e) => {
    if (isDragging && dragPosition.offsetX !== undefined) {
      e.preventDefault();
      e.stopPropagation();

      const newX = Math.max(0, Math.min(window.innerWidth - 320, e.clientX - dragPosition.offsetX));
      const newY = Math.max(0, Math.min(window.innerHeight - 120, e.clientY - dragPosition.offsetY));

      setDragPosition(prev => ({
        ...prev,
        x: newX,
        y: newY
      }));
    }
  };

  const handleMouseUp = (e) => {
    e.stopPropagation();
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragPosition.offsetX]);

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay pour fermer le menu - sous la carte */}
      <div
        className="fixed inset-0 z-[999]"
        onClick={onClose}
      />

      {/* Menu contextuel compact et draggable */}
      <motion.div
        ref={menuRef}
        initial={{ opacity: 0, scale: 0.9, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 10 }}
        transition={{ duration: 0.15, ease: "easeOut" }}
        className="fixed z-[1000] overflow-hidden select-none"
        onMouseDown={(e) => {
          e.stopPropagation();
          e.preventDefault();
          handleMouseDown(e);
        }}
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onMouseUp={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onMouseMove={(e) => {
          e.stopPropagation();
        }}
        style={{
          left: dragPosition.x || Math.min(position.x, window.innerWidth - 280),
          top: dragPosition.y || Math.min(position.y, window.innerHeight - 120),
          width: targetElement && (targetElement.options?.icon || targetElement._icon || (typeof targetElement.getLatLng === 'function' && !targetElement.getRadius)) ? '240px' : '260px',
          transform: `scale(${Math.max(0.8, Math.min(1.2, zoomLevel / 10))})`,
          transformOrigin: 'top left',
          background: 'linear-gradient(135deg, #1a202c 0%, #2d3748 100%)',
          borderRadius: '12px',
          border: '1px solid #68d391',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(104, 211, 145, 0.2)',
          cursor: isDragging ? 'grabbing' : 'grab'
        }}
      >
        {/* Interface adaptative */}
        <div className="p-3">

          {/* Design vertical pour marqueurs */}
          {targetElement && (targetElement.options?.icon || targetElement._icon || (typeof targetElement.getLatLng === 'function' && !targetElement.getRadius)) ? (
            <div className="space-y-3">
              {/* Icônes en haut */}
              <div className="text-center">
                <div className="text-xs text-white font-medium mb-2">Icône:</div>
                <div className="flex justify-center space-x-2">
                  {markerIcons.map((icon, index) => (
                    <button
                      key={index}
                      onClick={() => handleIconSelect(icon)}
                      className="w-8 h-8 hover:bg-white/20 rounded-lg transition-all bg-white/10 hover:scale-110 flex items-center justify-center"
                      title={`Icône ${icon}`}
                    >
                      <span className="text-lg">{icon}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Commentaire */}
              <div>
                <div className="text-xs text-white font-medium mb-2">Commentaire:</div>
                <input
                  type="text"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  onBlur={handleCommentSave}
                  placeholder="Commentaire..."
                  className="w-full px-3 py-2 text-xs bg-white/90 text-black rounded border focus:border-blue-400 focus:outline-none"
                />
              </div>

              {/* Position */}
              <div>
                <div className="text-xs text-white font-medium mb-2">Position:</div>
                <div className="flex space-x-2">
                  <input
                    type="number"
                    step="0.000001"
                    value={markerLat}
                    onChange={(e) => setMarkerLat(e.target.value)}
                    onBlur={handlePositionChange}
                    className="flex-1 px-2 py-1 text-xs bg-white/90 text-black rounded border focus:border-blue-400 focus:outline-none"
                    placeholder="Latitude"
                  />
                  <input
                    type="number"
                    step="0.000001"
                    value={markerLng}
                    onChange={(e) => setMarkerLng(e.target.value)}
                    onBlur={handlePositionChange}
                    className="flex-1 px-2 py-1 text-xs bg-white/90 text-black rounded border focus:border-blue-400 focus:outline-none"
                    placeholder="Longitude"
                  />
                </div>
              </div>
            </div>
          ) : (
            /* Design horizontal pour cercles/polygones */
            <div className="flex items-center space-x-3">
              {/* Couleur */}
              <div className="flex items-center space-x-2">
                <span className="text-xs text-white font-medium">Couleur:</span>
                <div className="flex space-x-1">
                  {colors.map((color) => (
                    <button
                      key={color.value}
                      onClick={() => handleColorSelect(color.value)}
                      className={`w-6 h-6 rounded border-2 transition-all ${
                        selectedColor === color.value
                          ? 'border-white scale-110'
                          : 'border-gray-400 hover:border-gray-300'
                      }`}
                      style={{ backgroundColor: color.value }}
                      title={color.name}
                    />
                  ))}
                </div>
              </div>

              {/* Commentaire */}
              <div className="flex items-center space-x-2 flex-1">
                <span className="text-xs text-white font-medium">Commentaire:</span>
                <input
                  type="text"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  onBlur={handleCommentSave}
                  placeholder="Commentaire..."
                  className="flex-1 px-3 py-1 text-xs bg-white/90 text-black rounded border focus:border-blue-400 focus:outline-none"
                />
              </div>
            </div>
          )}

        </div>

        {/* Bouton supprimer compact */}
        <div className="border-t border-white/10 p-2">
          <button
            onClick={handleDelete}
            className="w-full py-2 bg-red-500 hover:bg-red-600 rounded-lg flex items-center justify-center space-x-2 transition-all text-white text-xs font-medium"
            title="Supprimer"
          >
            <Trash2 className="w-3 h-3" />
            <span>Supprimer</span>
          </button>
        </div>
      </motion.div>
    </>
  );
};

export default ContextMenu;
