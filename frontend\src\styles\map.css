/* Styles pour les tooltips des régions du Maroc */
.morocco-regions-tooltip {
  background: rgba(0, 0, 0, 0.8) !important;
  border: 1px solid #3b82f6 !important;
  border-radius: 6px !important;
  color: white !important;
  font-size: 12px !important;
  padding: 8px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
}

/* Styles pour les tooltips de commentaires - FOND BLANC */
.comment-tooltip {
  background: white !important;
  border: 2px solid #374151 !important;
  border-radius: 8px !important;
  color: #1f2937 !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  padding: 10px 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  max-width: 250px !important;
  word-wrap: break-word !important;
}

.morocco-regions-tooltip::before {
  border-top-color: #3b82f6 !important;
}

/* Flèche du tooltip commentaire */
.comment-tooltip::before {
  border-top-color: #374151 !important;
}

/* Styles pour les popups tactiques - FOND BLANC */
.tactical-popup {
  background: white !important;
  color: #1f2937 !important;
  font-family: 'Inter', sans-serif !important;
  font-size: 13px !important;
  line-height: 1.4 !important;
  padding: 12px !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 2px solid #374151 !important;
  min-width: 200px !important;
  max-width: 300px !important;
}

.tactical-popup .comment-section {
  background: #f8fafc !important;
  padding: 8px !important;
  border-radius: 6px !important;
  margin-bottom: 8px !important;
  border-left: 3px solid #3b82f6 !important;
}

.tactical-popup .info-section {
  background: #f1f5f9 !important;
  padding: 8px !important;
  border-radius: 6px !important;
  border-left: 3px solid #10b981 !important;
}

.tactical-popup strong {
  color: #374151 !important;
  font-weight: 600 !important;
}

.tactical-popup div {
  margin-bottom: 4px !important;
}

.tactical-popup div:last-child {
  margin-bottom: 0 !important;
}

/* Style global pour tous les popups Leaflet */
.leaflet-popup-content-wrapper {
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 2px solid #374151 !important;
}

.leaflet-popup-content {
  margin: 0 !important;
  line-height: 1.4 !important;
  font-size: 13px !important;
  color: #1f2937 !important;
}

.leaflet-popup-tip {
  background: white !important;
  border: 2px solid #374151 !important;
  border-top: none !important;
  border-right: none !important;
}

/* Style pour les popups de mesure */
.measurement-result {
  background: white !important;
  color: #1f2937 !important;
  padding: 8px !important;
  border-radius: 6px !important;
  text-align: center !important;
  font-weight: 500 !important;
}

/* Styles pour les couches de carte */
.leaflet-control-layers {
  background: rgba(31, 41, 55, 0.9) !important;
  border: 1px solid #374151 !important;
  border-radius: 8px !important;
  color: white !important;
}

.leaflet-control-layers-toggle {
  background-color: #1f2937 !important;
  border-radius: 6px !important;
}

/* Animation pour les frontières */
.morocco-border-animation {
  animation: borderPulse 3s ease-in-out infinite;
}

@keyframes borderPulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* Masquer les indices de debug des tuiles */
.leaflet-tile-debug,
.leaflet-tile-coords,
.leaflet-debug-grid,
.leaflet-tile::after,
.leaflet-tile::before {
  display: none !important;
  visibility: hidden !important;
}

/* Masquer tout texte sur les tuiles */
.leaflet-tile-container .leaflet-tile {
  position: relative;
}

.leaflet-tile-container .leaflet-tile::after,
.leaflet-tile-container .leaflet-tile::before {
  content: none !important;
  display: none !important;
}

/* Forcer l'affichage propre des tuiles */
.map-tiles .leaflet-tile {
  border: none !important;
  outline: none !important;
}

.map-tiles .leaflet-tile::after {
  display: none !important;
}

/* Style pour les tuiles en cours de chargement */
.leaflet-tile-loading {
  opacity: 0.3 !important;
}

/* Masquer les bordures des tuiles */
.local-tiles .leaflet-tile {
  border: none !important;
  box-shadow: none !important;
}
