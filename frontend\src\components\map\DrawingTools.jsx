import React, { useEffect, useRef, useState } from 'react';
import L from 'leaflet';
import ContextMenu from './ContextMenu';
import localStorageService from '@/services/localStorageService';

const DrawingToolsNew = ({ activeTool, map }) => {
  const drawnItemsRef = useRef(null);
  const currentDrawingRef = useRef(null);

  // États pour le menu contextuel
  const [contextMenu, setContextMenu] = useState({
    isVisible: false,
    position: { x: 0, y: 0 },
    targetElement: null
  });

  useEffect(() => {
    if (!map) return;

    // Créer le groupe pour les éléments dessinés
    if (!drawnItemsRef.current) {
      drawnItemsRef.current = new L.FeatureGroup();
      map.addLayer(drawnItemsRef.current);

      // Charger les dessins existants depuis localStorage
      setTimeout(() => {
        loadExistingDrawings();
      }, 100);
    }

    // Nettoyer les événements précédents
    map.off('click');
    map.off('mousemove');
    map.off('contextmenu');

    // Gérer les outils selon l'outil actif
    if (activeTool && typeof activeTool === 'object') {
      const { type, color } = activeTool;
      handleToolActivation(type, color);
    } else if (typeof activeTool === 'string') {
      handleToolActivation(activeTool, '#ff0000'); // Couleur par défaut
    }

    return () => {
      // Nettoyer lors du démontage
      if (currentDrawingRef.current) {
        map.removeLayer(currentDrawingRef.current);
        currentDrawingRef.current = null;
      }
    };
  }, [activeTool, map]);

  // Sauvegarder automatiquement toutes les 30 secondes
  useEffect(() => {
    const interval = setInterval(() => {
      if (drawnItemsRef.current) {
        const layers = drawnItemsRef.current.getLayers();
        layers.forEach(layer => {
          if (!layer._drawingId) {
            saveElement(layer);
          }
        });
      }
    }, 30000); // 30 secondes

    return () => clearInterval(interval);
  }, []);

  // Charger les dessins existants depuis le localStorage
  const loadExistingDrawings = () => {
    try {
      const drawings = localStorageService.getAllDrawings();
      console.log(`🔄 Chargement de ${drawings.length} dessins sauvegardés`);

      drawings.forEach(drawing => {
        const leafletObject = localStorageService.storageToLeaflet(drawing, L);
        if (leafletObject) {
          addContextMenuToElement(leafletObject);
          drawnItemsRef.current.addLayer(leafletObject);
        }
      });
    } catch (error) {
      console.error('Erreur chargement dessins:', error);
    }
  };

  // Ajouter le menu contextuel à un élément
  const addContextMenuToElement = (element) => {
    element.on('contextmenu', (e) => {
      e.originalEvent.preventDefault();
      setContextMenu({
        isVisible: true,
        position: { x: e.originalEvent.clientX, y: e.originalEvent.clientY },
        targetElement: element
      });
    });
  };

  // Gestionnaires du menu contextuel
  const handleContextMenuClose = () => {
    setContextMenu({ isVisible: false, position: { x: 0, y: 0 }, targetElement: null });
  };

  const handleColorChange = (color) => {
    if (contextMenu.targetElement) {
      contextMenu.targetElement.setStyle({ color: color, fillColor: color });
      // Sauvegarder automatiquement
      saveElement(contextMenu.targetElement);
    }
  };

  const handleCommentChange = (comment) => {
    if (contextMenu.targetElement) {
      // Stocker le commentaire dans l'élément
      contextMenu.targetElement._comment = comment;

      // Mettre à jour le popup avec le nouveau commentaire
      const formattedContent = formatPopupContent(contextMenu.targetElement, comment);
      contextMenu.targetElement.bindPopup(formattedContent);

      // Configurer le tooltip pour affichage au survol
      if (comment) {
        contextMenu.targetElement.bindTooltip(comment, {
          permanent: false,
          direction: 'top',
          className: 'comment-tooltip'
        });
      } else {
        contextMenu.targetElement.unbindTooltip();
      }

      // Sauvegarder automatiquement
      saveElement(contextMenu.targetElement);
    }
  };

  const handleElementDelete = () => {
    if (contextMenu.targetElement && contextMenu.targetElement._drawingId) {
      // Supprimer du localStorage
      localStorageService.deleteDrawing(contextMenu.targetElement._drawingId);
      // Supprimer de la carte
      drawnItemsRef.current.removeLayer(contextMenu.targetElement);
    }
  };

  const handleElementSave = () => {
    if (contextMenu.targetElement) {
      saveElement(contextMenu.targetElement);
    }
  };

  const handleIconChange = (icon) => {
    if (contextMenu.targetElement && typeof contextMenu.targetElement.getLatLng === 'function') {
      // Mettre à jour l'icône du marqueur
      const newIcon = L.divIcon({
        html: `<div style="background: #4299e1; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center; font-size: 12px;">${icon}</div>`,
        className: 'custom-marker',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
      });
      contextMenu.targetElement.setIcon(newIcon);

      // Sauvegarder automatiquement
      saveElement(contextMenu.targetElement);
    }
  };

  // Sauvegarder un élément
  const saveElement = (element) => {
    try {
      const drawingType = getElementType(element);
      const drawing = localStorageService.leafletToStorage(element, drawingType);

      if (element._drawingId) {
        // Mise à jour
        localStorageService.updateDrawing(element._drawingId, drawing);
      } else {
        // Nouvelle sauvegarde
        const result = localStorageService.saveDrawing(drawing);
        element._drawingId = result.id;
      }
    } catch (error) {
      console.error('Erreur sauvegarde:', error);
    }
  };

  // Déterminer le type d'élément
  const getElementType = (element) => {
    if (element instanceof L.Circle) return 'circle';
    if (element instanceof L.Polygon) return 'polygon';
    if (element instanceof L.Polyline) return 'polyline';
    if (element instanceof L.Marker) return 'marker';
    return 'unknown';
  };

  // Formater le contenu du popup avec commentaire au survol
  const formatPopupContent = (element, comment = '') => {
    let content = '<div class="tactical-popup">';

    // Commentaire en premier si présent
    if (comment) {
      content += `<div class="comment-section"><strong>Commentaire:</strong><br>${comment}</div>`;
    }

    // Informations techniques
    if (element instanceof L.Circle) {
      const radius = element.getRadius();
      const area = Math.PI * radius * radius / 1000000;
      content += `<div class="info-section">`;
      content += `<div><strong>Rayon:</strong> ${(radius/1000).toFixed(2)} km</div>`;
      content += `<div><strong>Surface:</strong> ${area.toFixed(2)} km²</div>`;
      content += `</div>`;
    }

    if (element instanceof L.Polygon) {
      const area = calculatePolygonArea(element.getLatLngs()[0]);
      content += `<div class="info-section">`;
      content += `<div><strong>Surface:</strong> ${area.toFixed(2)} km²</div>`;
      content += `</div>`;
    }

    if (element instanceof L.Polyline && !(element instanceof L.Polygon)) {
      const distance = calculateDistance(element.getLatLngs());
      content += `<div class="info-section">`;
      content += `<div><strong>Distance:</strong> ${distance.toFixed(2)} km</div>`;
      content += `</div>`;
    }

    content += '</div>';
    return content;
  };

  const handleToolActivation = (toolType, color) => {
    if (!map) return;

    // Définir les styles selon la couleur choisie
    const shapeOptions = {
      color: color,
      fillColor: color,
      fillOpacity: 0.2,
      weight: 3,
      opacity: 0.8,
    };

    switch (toolType) {
      case 'circle':
        activateCircleTool(shapeOptions);
        break;
      case 'polygon':
        activatePolygonTool(shapeOptions);
        break;

      case 'trajectory':
        activateTrajectoryTool(shapeOptions);
        break;
      case 'distance':
        activateDistanceTool(shapeOptions);
        break;
      case 'marker':
        activateMarkerTool(shapeOptions);
        break;
      default:
        break;
    }
  };

  const activateCircleTool = (options) => {
    let center = null;
    let circle = null;
    let radiusTooltip = null;

    const onMapClick = (e) => {
      if (!center) {
        // Premier clic - définir le centre
        center = e.latlng;
        circle = L.circle(center, { radius: 100, ...options });
        map.addLayer(circle);
        currentDrawingRef.current = circle;

        // Créer tooltip pour affichage du rayon
        radiusTooltip = L.tooltip({
          permanent: true,
          direction: 'top',
          className: 'radius-tooltip'
        }).setLatLng(center).setContent('Rayon: 0.10 km');
        map.addLayer(radiusTooltip);
      } else {
        // Deuxième clic - finaliser le cercle
        const radius = center.distanceTo(e.latlng);
        circle.setRadius(radius);
        drawnItemsRef.current.addLayer(circle);

        // Supprimer le tooltip temporaire
        if (radiusTooltip) {
          map.removeLayer(radiusTooltip);
        }

        // Ajouter popup avec informations professionnelles
        const popupContent = formatPopupContent(circle);
        circle.bindPopup(popupContent);

        // Ajouter menu contextuel
        addContextMenuToElement(circle);

        // Sauvegarder automatiquement
        saveElement(circle);

        // Nettoyer
        map.off('click', onMapClick);
        map.off('mousemove', onMouseMove);
        currentDrawingRef.current = null;
      }
    };

    const onMouseMove = (e) => {
      if (center && circle) {
        const radius = center.distanceTo(e.latlng);
        circle.setRadius(radius);

        // Mettre à jour le tooltip du rayon
        if (radiusTooltip) {
          radiusTooltip.setContent(`Rayon: ${(radius/1000).toFixed(2)} km`);
          radiusTooltip.setLatLng(e.latlng);
        }
      }
    };

    map.on('click', onMapClick);
    map.on('mousemove', onMouseMove);
  };

  const activatePolygonTool = (options) => {
    let points = [];
    let polygon = null;
    let tempLine = null;

    const onMapClick = (e) => {
      points.push(e.latlng);

      if (points.length === 1) {
        // Premier point
        tempLine = L.polyline(points, { ...options, fillOpacity: 0 });
        map.addLayer(tempLine);
      } else if (points.length >= 3 && e.originalEvent.detail === 2) {
        // Double-clic pour terminer
        points.pop(); // Enlever le dernier point du double-clic
        
        if (tempLine) map.removeLayer(tempLine);
        
        polygon = L.polygon(points, options);
        drawnItemsRef.current.addLayer(polygon);
        
        // Calculer la surface
        const area = calculatePolygonArea(points);
        polygon.bindPopup(`Zone Polygonale<br>Surface: ${area.toFixed(2)} km²`);
        
        // Nettoyer
        map.off('click', onMapClick);
        map.off('mousemove', onMouseMove);
        points = [];
        currentDrawingRef.current = null;
      } else {
        // Points intermédiaires
        tempLine.setLatLngs(points);
      }
    };

    const onMouseMove = (e) => {
      if (points.length > 0 && tempLine) {
        const currentPoints = [...points, e.latlng];
        tempLine.setLatLngs(currentPoints);
      }
    };

    map.on('click', onMapClick);
    map.on('mousemove', onMouseMove);
    currentDrawingRef.current = tempLine;
  };

  const activateDistanceTool = (options) => {
    let startPoint = null;
    let tempLine = null;
    let distanceTooltip = null;

    const onMapClick = (e) => {
      if (!startPoint) {
        // Premier clic - point de départ
        startPoint = e.latlng;
        tempLine = L.polyline([startPoint, startPoint], {
          ...options,
          fillOpacity: 0,
          dashArray: '5, 10',
          weight: 2,
          opacity: 0.7
        });
        map.addLayer(tempLine);
        currentDrawingRef.current = tempLine;

        // Créer tooltip pour affichage de la distance
        distanceTooltip = L.tooltip({
          permanent: true,
          direction: 'top',
          className: 'distance-tooltip'
        }).setLatLng(startPoint).setContent('Distance: 0.00 km');
        map.addLayer(distanceTooltip);
      } else {
        // Deuxième clic - finaliser la mesure
        const distance = startPoint.distanceTo(e.latlng);

        // Supprimer la ligne temporaire et le tooltip
        if (tempLine) {
          map.removeLayer(tempLine);
        }
        if (distanceTooltip) {
          map.removeLayer(distanceTooltip);
        }

        // Afficher le résultat final dans un popup temporaire
        const resultPopup = L.popup()
          .setLatLng(e.latlng)
          .setContent(`<div class="measurement-result">
            <strong>Distance mesurée:</strong><br>
            ${(distance/1000).toFixed(2)} km
          </div>`)
          .openOn(map);

        // Fermer le popup après 3 secondes
        setTimeout(() => {
          map.closePopup(resultPopup);
        }, 3000);

        // Nettoyer
        map.off('click', onMapClick);
        map.off('mousemove', onMouseMove);
        currentDrawingRef.current = null;
      }
    };

    const onMouseMove = (e) => {
      if (startPoint && tempLine) {
        const distance = startPoint.distanceTo(e.latlng);
        tempLine.setLatLngs([startPoint, e.latlng]);

        // Mettre à jour le tooltip de distance
        if (distanceTooltip) {
          distanceTooltip.setContent(`Distance: ${(distance/1000).toFixed(2)} km`);
          distanceTooltip.setLatLng(e.latlng);
        }
      }
    };

    map.on('click', onMapClick);
    map.on('mousemove', onMouseMove);
  };



  // Outil de trajectoire multi-points
  const activateTrajectoryTool = (options) => {
    let points = [];
    let trajectory = null;
    let distanceTooltip = null;
    let totalDistance = 0;

    const onMapClick = (e) => {
      points.push(e.latlng);

      if (points.length === 1) {
        // Premier point
        trajectory = L.polyline(points, {
          ...options,
          fillOpacity: 0,
          weight: 3,
          dashArray: '10, 5'
        });
        map.addLayer(trajectory);
        currentDrawingRef.current = trajectory;

        // Créer tooltip pour distance totale
        distanceTooltip = L.tooltip({
          permanent: true,
          direction: 'top',
          className: 'distance-tooltip'
        }).setLatLng(e.latlng).setContent('Distance: 0.00 km');
        map.addLayer(distanceTooltip);
      } else if (e.originalEvent.detail === 2) {
        // Double-clic pour terminer
        points.pop(); // Enlever le dernier point du double-clic

        // Supprimer la trajectoire temporaire
        if (trajectory) {
          map.removeLayer(trajectory);
        }

        // Supprimer le tooltip temporaire
        if (distanceTooltip) {
          map.removeLayer(distanceTooltip);
        }

        // Afficher le résultat final dans un popup temporaire
        const resultPopup = L.popup()
          .setLatLng(points[points.length - 1])
          .setContent(`<div class="measurement-result">
            <strong>Trajectoire mesurée:</strong><br>
            ${(totalDistance/1000).toFixed(2)} km<br>
            <small>${points.length} points</small>
          </div>`)
          .openOn(map);

        // Fermer le popup après 4 secondes
        setTimeout(() => {
          map.closePopup(resultPopup);
        }, 4000);

        // Nettoyer
        map.off('click', onMapClick);
        map.off('mousemove', onMouseMove);
        currentDrawingRef.current = null;
        points = [];
        totalDistance = 0;
      } else {
        // Points intermédiaires
        trajectory.setLatLngs(points);

        // Calculer distance totale
        totalDistance = 0;
        for (let i = 0; i < points.length - 1; i++) {
          totalDistance += points[i].distanceTo(points[i + 1]);
        }
      }
    };

    const onMouseMove = (e) => {
      if (points.length > 0 && trajectory) {
        const currentPoints = [...points, e.latlng];
        trajectory.setLatLngs(currentPoints);

        // Calculer distance avec point temporaire
        let tempDistance = totalDistance;
        if (points.length > 0) {
          tempDistance += points[points.length - 1].distanceTo(e.latlng);
        }

        // Mettre à jour le tooltip
        if (distanceTooltip) {
          distanceTooltip.setContent(`Distance: ${(tempDistance/1000).toFixed(2)} km`);
          distanceTooltip.setLatLng(e.latlng);
        }
      }
    };

    map.on('click', onMapClick);
    map.on('mousemove', onMouseMove);
  };

  // Outil de marqueur
  const activateMarkerTool = () => {
    const onMapClick = (e) => {
      // Créer un marqueur personnalisé
      const marker = L.marker(e.latlng, {
        icon: L.divIcon({
          html: '<div style="background: #4299e1; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; display: flex; align-items: center; justify-content: center; font-size: 12px;">📍</div>',
          className: 'custom-marker',
          iconSize: [20, 20],
          iconAnchor: [10, 10]
        })
      });

      drawnItemsRef.current.addLayer(marker);

      // Ajouter popup par défaut
      marker.bindPopup('Nouveau marqueur<br><small>Clic droit pour modifier</small>');

      // Ajouter menu contextuel
      addContextMenuToElement(marker);

      // Sauvegarder automatiquement
      saveElement(marker);

      // Nettoyer après un seul clic
      map.off('click', onMapClick);
    };

    map.on('click', onMapClick);
  };

  const calculatePolygonArea = (latlngs) => {
    // Calcul approximatif de la surface en km²
    if (latlngs.length < 3) return 0;
    
    let area = 0;
    const earthRadius = 6371000; // Rayon de la Terre en mètres
    
    for (let i = 0; i < latlngs.length; i++) {
      const j = (i + 1) % latlngs.length;
      const lat1 = latlngs[i].lat * Math.PI / 180;
      const lat2 = latlngs[j].lat * Math.PI / 180;
      const lng1 = latlngs[i].lng * Math.PI / 180;
      const lng2 = latlngs[j].lng * Math.PI / 180;
      
      area += (lng2 - lng1) * (2 + Math.sin(lat1) + Math.sin(lat2));
    }
    
    area = Math.abs(area * earthRadius * earthRadius / 2);
    return area / 1000000; // Convertir en km²
  };

  return (
    <>
      {/* Menu contextuel */}
      <ContextMenu
        isVisible={contextMenu.isVisible}
        position={contextMenu.position}
        targetElement={contextMenu.targetElement}
        onClose={handleContextMenuClose}
        onColorChange={handleColorChange}
        onCommentChange={handleCommentChange}
        onIconChange={handleIconChange}
        onDelete={handleElementDelete}
        onSave={handleElementSave}
      />
    </>
  );
};

export default DrawingToolsNew;
