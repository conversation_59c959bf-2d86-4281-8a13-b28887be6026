import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Palette, MessageSquare, Trash2, Save, Edit3 } from 'lucide-react';

const ContextMenu = ({
  isVisible,
  position,
  onClose,
  targetElement,
  onColorChange,
  onCommentChange,
  onIconChange,
  onDelete,
  onSave
}) => {
  const [selectedColor, setSelectedColor] = useState('#ff0000');
  const [comment, setComment] = useState('');
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showCommentEditor, setShowCommentEditor] = useState(false);
  const [showIconPicker, setShowIconPicker] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState('📍');
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const menuRef = useRef(null);

  const colors = [
    { value: '#ff0000', name: '<PERSON>' },
    { value: '#00ff00', name: 'Vert' },
    { value: '#0000ff', name: 'Bleu' }
  ];

  const markerIcons = ['📍', '🎯', '⚠️', '🚩'];

  useEffect(() => {
    if (isVisible && targetElement) {
      // Récupérer les propriétés actuelles de l'élément
      if (targetElement.options) {
        setSelectedColor(targetElement.options.color || '#ff0000');
      }

      // Récupérer le commentaire stocké directement
      const storedComment = targetElement._comment || '';
      setComment(storedComment);
    }
  }, [isVisible, targetElement]);

  const handleColorSelect = (color) => {
    setSelectedColor(color);
    if (onColorChange) {
      onColorChange(color);
    }
    setShowColorPicker(false);
  };

  const handleCommentSave = () => {
    if (onCommentChange) {
      onCommentChange(comment);
    }
    setShowCommentEditor(false);
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
    onClose();
  };

  const handleSave = () => {
    if (onSave) {
      onSave();
    }
    onClose();
  };

  const handleIconSelect = (icon) => {
    setSelectedIcon(icon);
    if (onIconChange) {
      onIconChange(icon);
    }
    setShowIconPicker(false);
  };

  // Gestionnaires de drag
  const handleMouseDown = (e) => {
    setIsDragging(true);
    const rect = menuRef.current.getBoundingClientRect();
    setDragPosition({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging]);

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay pour fermer le menu */}
      <div
        className="fixed inset-0 z-[9999]"
        onClick={onClose}
      />

      {/* Menu contextuel compact et fluide */}
      <motion.div
        ref={menuRef}
        initial={{ opacity: 0, scale: 0.9, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 10 }}
        transition={{ duration: 0.15, ease: "easeOut" }}
        className="fixed z-[10000] overflow-hidden select-none"
        onMouseDown={handleMouseDown}
        style={{
          left: Math.min(position.x, window.innerWidth - 220),
          top: Math.min(position.y, window.innerHeight - 280),
          width: '200px',
          maxHeight: '260px',
          background: 'linear-gradient(135deg, #2d3748 0%, #4a5568 100%)',
          borderRadius: '12px',
          border: '1px solid #68d391',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(104, 211, 145, 0.2)',
          cursor: isDragging ? 'grabbing' : 'grab'
        }}
      >
        {/* Interface compacte */}
        <div className="p-3">

          {/* Couleur - seulement pour cercles et polygones */}
          {targetElement && !targetElement.options?.icon && (
            <div className="mb-3">
              <div className="text-xs font-medium text-c2-gray-100 mb-2">Couleur:</div>
              <div className="flex space-x-2">
                {colors.map((color) => (
                  <button
                    key={color.value}
                    onClick={() => handleColorSelect(color.value)}
                    className={`w-6 h-6 rounded border-2 transition-all ${
                      selectedColor === color.value
                        ? 'border-white scale-110'
                        : 'border-gray-400 hover:border-gray-300'
                    }`}
                    style={{ backgroundColor: color.value }}
                    title={color.name}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Commentaire */}
          <div className="mb-3">
            <div className="text-xs font-medium text-c2-gray-100 mb-2">Commentaire:</div>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              onBlur={handleCommentSave}
              placeholder="Commentaire..."
              className="w-full p-2 text-xs bg-c2-gray-200 text-c2-black rounded resize-none border border-c2-gray-100 focus:border-c2-blue focus:outline-none"
              rows={2}
            />
          </div>

          {/* Icônes pour marqueurs seulement */}
          {targetElement && (targetElement.options?.icon || targetElement._icon || (typeof targetElement.getLatLng === 'function' && !targetElement.getRadius)) && (
            <div className="mb-3">
              <div className="text-xs font-medium text-c2-gray-100 mb-2">Icône:</div>
              <div className="grid grid-cols-4 gap-1">
                {markerIcons.map((icon, index) => (
                  <button
                    key={index}
                    onClick={() => handleIconSelect(icon)}
                    className="p-1 text-lg hover:bg-c2-gray-200 rounded transition-all bg-c2-gray-300 hover:scale-110"
                    title={`Icône ${icon}`}
                  >
                    {icon}
                  </button>
                ))}
              </div>
            </div>
          )}

        </div>

        {/* Bouton supprimer compact */}
        <div className="border-t border-c2-gray-300 p-2">
          <button
            onClick={handleDelete}
            className="w-full p-2 flex items-center justify-center space-x-1 bg-red-600 hover:bg-red-700 rounded transition-colors text-white text-xs"
          >
            <Trash2 className="w-3 h-3" />
            <span>Supprimer</span>
          </button>
        </div>
      </motion.div>
    </>
  );
};

export default ContextMenu;
