// Service de sauvegarde locale pour les dessins tactiques
class LocalStorageService {
  constructor() {
    this.storageKey = 'tactical_drawings';
    this.sessionKey = 'tactical_session';
  }

  // Sauvegarder un dessin
  saveDrawing(drawing) {
    try {
      const drawings = this.getAllDrawings();
      const drawingWithId = {
        ...drawing,
        id: drawing.id || this.generateId(),
        timestamp: new Date().toISOString(),
        session: this.getSessionId()
      };
      
      // Vérifier si le dessin existe déjà
      const existingIndex = drawings.findIndex(d => d.id === drawingWithId.id);
      
      if (existingIndex >= 0) {
        // Mettre à jour
        drawings[existingIndex] = { ...drawings[existingIndex], ...drawingWithId };
      } else {
        // Ajouter nouveau
        drawings.push(drawingWithId);
      }
      
      localStorage.setItem(this.storageKey, JSON.stringify(drawings));
      console.log('✅ Dessin sauvegardé localement:', drawingWithId.id);
      return drawingWithId;
    } catch (error) {
      console.error('❌ Erreur sauvegarde locale:', error);
      throw error;
    }
  }

  // Récupérer tous les dessins
  getAllDrawings() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('❌ Erreur lecture dessins:', error);
      return [];
    }
  }

  // Récupérer un dessin par ID
  getDrawing(id) {
    const drawings = this.getAllDrawings();
    return drawings.find(d => d.id === id);
  }

  // Supprimer un dessin
  deleteDrawing(id) {
    try {
      const drawings = this.getAllDrawings();
      const filteredDrawings = drawings.filter(d => d.id !== id);
      localStorage.setItem(this.storageKey, JSON.stringify(filteredDrawings));
      console.log('✅ Dessin supprimé:', id);
      return true;
    } catch (error) {
      console.error('❌ Erreur suppression:', error);
      return false;
    }
  }

  // Mettre à jour un dessin
  updateDrawing(id, updates) {
    try {
      const drawings = this.getAllDrawings();
      const index = drawings.findIndex(d => d.id === id);
      
      if (index >= 0) {
        drawings[index] = { 
          ...drawings[index], 
          ...updates, 
          updatedAt: new Date().toISOString() 
        };
        localStorage.setItem(this.storageKey, JSON.stringify(drawings));
        console.log('✅ Dessin mis à jour:', id);
        return drawings[index];
      }
      
      return null;
    } catch (error) {
      console.error('❌ Erreur mise à jour:', error);
      throw error;
    }
  }

  // Nettoyer les anciens dessins (plus de 30 jours)
  cleanOldDrawings() {
    try {
      const drawings = this.getAllDrawings();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentDrawings = drawings.filter(d => {
        const drawingDate = new Date(d.timestamp);
        return drawingDate > thirtyDaysAgo;
      });
      
      localStorage.setItem(this.storageKey, JSON.stringify(recentDrawings));
      console.log(`🧹 Nettoyage: ${drawings.length - recentDrawings.length} dessins supprimés`);
    } catch (error) {
      console.error('❌ Erreur nettoyage:', error);
    }
  }

  // Exporter tous les dessins
  exportDrawings() {
    const drawings = this.getAllDrawings();
    const exportData = {
      version: '1.0',
      exportDate: new Date().toISOString(),
      drawings: drawings
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tactical_drawings_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }

  // Importer des dessins
  importDrawings(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const importData = JSON.parse(e.target.result);
          
          if (importData.drawings && Array.isArray(importData.drawings)) {
            const currentDrawings = this.getAllDrawings();
            const mergedDrawings = [...currentDrawings];
            
            importData.drawings.forEach(drawing => {
              const existingIndex = mergedDrawings.findIndex(d => d.id === drawing.id);
              if (existingIndex >= 0) {
                mergedDrawings[existingIndex] = drawing;
              } else {
                mergedDrawings.push(drawing);
              }
            });
            
            localStorage.setItem(this.storageKey, JSON.stringify(mergedDrawings));
            console.log(`✅ ${importData.drawings.length} dessins importés`);
            resolve(importData.drawings.length);
          } else {
            reject(new Error('Format de fichier invalide'));
          }
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => reject(new Error('Erreur lecture fichier'));
      reader.readAsText(file);
    });
  }

  // Utilitaires
  generateId() {
    return `tactical_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getSessionId() {
    let sessionId = sessionStorage.getItem(this.sessionKey);
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem(this.sessionKey, sessionId);
    }
    return sessionId;
  }

  // Statistiques
  getStats() {
    const drawings = this.getAllDrawings();
    const stats = {
      total: drawings.length,
      byType: {},
      byDate: {},
      totalSize: JSON.stringify(drawings).length
    };

    drawings.forEach(drawing => {
      // Par type
      stats.byType[drawing.type] = (stats.byType[drawing.type] || 0) + 1;
      
      // Par date
      const date = new Date(drawing.timestamp).toDateString();
      stats.byDate[date] = (stats.byDate[date] || 0) + 1;
    });

    return stats;
  }

  // Convertir un objet Leaflet en format de sauvegarde
  leafletToStorage(leafletObject, type) {
    const data = {
      type: type,
      coordinates: this.extractCoordinates(leafletObject, type),
      style: leafletObject.options || {},
      comment: this.extractComment(leafletObject)
    };

    // Propriétés spécifiques selon le type
    switch (type) {
      case 'circle':
        data.radius = leafletObject.getRadius();
        data.center = [leafletObject.getLatLng().lat, leafletObject.getLatLng().lng];
        data.area = Math.PI * Math.pow(leafletObject.getRadius(), 2) / 1000000; // km²
        break;

      case 'polygon':
        data.area = this.calculatePolygonArea(leafletObject.getLatLngs()[0]);
        break;

      case 'marker':
        // Extraire l'icône du HTML du divIcon
        const iconHtml = leafletObject.options.icon?.options?.html || '';
        const iconMatch = iconHtml.match(/>(.*?)</);
        data.icon = iconMatch ? iconMatch[1] : '📍';
        break;
    }

    return data;
  }

  // Extraire le commentaire du popup
  extractComment(leafletObject) {
    // Utiliser le commentaire stocké directement dans l'objet
    if (leafletObject._comment) {
      return leafletObject._comment;
    }

    if (leafletObject.getPopup && leafletObject.getPopup()) {
      const content = leafletObject.getPopup().getContent();
      if (typeof content === 'string') {
        // Extraire juste le commentaire, pas les infos techniques
        const commentMatch = content.match(/<strong>Commentaire:<\/strong><br>(.*?)(?:<\/div|$)/i);
        return commentMatch ? commentMatch[1].trim() : '';
      }
    }
    return '';
  }

  // Calculer l'aire d'un polygone
  calculatePolygonArea(latlngs) {
    if (latlngs.length < 3) return 0;

    let area = 0;
    const earthRadius = 6371000; // Rayon de la Terre en mètres

    for (let i = 0; i < latlngs.length; i++) {
      const j = (i + 1) % latlngs.length;
      const lat1 = latlngs[i].lat * Math.PI / 180;
      const lat2 = latlngs[j].lat * Math.PI / 180;
      const lng1 = latlngs[i].lng * Math.PI / 180;
      const lng2 = latlngs[j].lng * Math.PI / 180;

      area += (lng2 - lng1) * (2 + Math.sin(lat1) + Math.sin(lat2));
    }

    area = Math.abs(area * earthRadius * earthRadius / 2);
    return area / 1000000; // km²
  }

  // Extraire les coordonnées selon le type
  extractCoordinates(leafletObject, type) {
    switch (type) {
      case 'circle':
        // Pour les cercles, on sauvegarde le centre ET le rayon séparément
        return [leafletObject.getLatLng().lat, leafletObject.getLatLng().lng];

      case 'marker':
        return [leafletObject.getLatLng().lat, leafletObject.getLatLng().lng];

      case 'polygon':
        return leafletObject.getLatLngs()[0].map(latlng => [latlng.lat, latlng.lng]);

      case 'polyline':
        return leafletObject.getLatLngs().map(latlng => [latlng.lat, latlng.lng]);

      default:
        return [];
    }
  }

  // Créer un objet Leaflet depuis les données sauvegardées
  storageToLeaflet(data, L) {
    let leafletObject;

    switch (data.type) {
      case 'circle':
        // Utiliser le centre et le rayon sauvegardés
        const center = data.center || data.coordinates;
        const radius = data.radius || 1000; // Rayon par défaut si manquant
        leafletObject = L.circle(center, {
          radius: radius,
          ...data.style
        });
        console.log('🔄 Cercle restauré:', { center, radius, style: data.style });
        break;

      case 'polygon':
        leafletObject = L.polygon(data.coordinates, data.style);
        break;

      case 'polyline':
        leafletObject = L.polyline(data.coordinates, data.style);
        break;

      case 'marker':
        const icon = data.icon || '📍';
        leafletObject = L.marker(data.coordinates, {
          icon: L.divIcon({
            html: `<div style="font-size: 24px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">${icon}</div>`,
            className: 'icon-only-marker',
            iconSize: [24, 24],
            iconAnchor: [12, 12]
          })
        });
        break;

      default:
        return null;
    }

    // Ajouter le popup avec commentaire et infos techniques
    if (data.comment || data.area || data.radius) {
      const popupContent = this.formatPopupContent(data);
      leafletObject.bindPopup(popupContent);
    }

    // Ajouter l'ID pour la gestion
    leafletObject._drawingId = data.id;

    return leafletObject;
  }

  // Formater le contenu du popup
  formatPopupContent(data) {
    let content = '<div class="tactical-popup">';

    if (data.comment) {
      content += `<div><strong>Commentaire:</strong> ${data.comment}</div>`;
    }

    if (data.radius) {
      content += `<div><strong>Rayon:</strong> ${(data.radius/1000).toFixed(2)} km</div>`;
    }

    if (data.area) {
      content += `<div><strong>Surface:</strong> ${data.area.toFixed(2)} km²</div>`;
    }

    content += '</div>';
    return content;
  }
}

// Instance singleton
const localStorageService = new LocalStorageService();
export default localStorageService;
